{"name": "imgdreamsupport", "version": "1.0.0", "private": true, "workspaces": ["client", "server", "platform"], "scripts": {"dev:client": "cd client && npm run dev", "dev:server": "cd server && npm run dev", "dev": "concurrently \"npm run dev:server\" \"npm run dev:client\"", "build:platform": "cd platform && npm run build", "build:client": "cd client && npm run build", "build:server": "cd server && npm run build", "build": "npm run build:platform && npm run build:server && npm run build:client", "start": "cd server && npm start", "install:all": "npm install && cd platform && npm install && cd ../client && npm install && cd ../server && npm install"}, "devDependencies": {"concurrently": "^6.2.0"}}