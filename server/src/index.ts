import express, { Request, Response } from 'express';
import cors from 'cors';
import { BlogPost, Service, SubscribeFormData, StudyPartnerProfile } from 'imgdreamsupport-platform/types';
import { fakeStudyPartners } from './fakeData';

// Import booking service and DB query (even if service functions are placeholders for now)
import * as bookingService from './services/bookingService';
import { query as dbQuery } from './db'; // Assuming query is exported from db/index.ts

const app = express();
const port = process.env.PORT || 5000;

app.use(cors());
app.use(express.json());

// Sample data
const blogPosts: BlogPost[] = [
  { id: '1', title: 'USMLE Step 1 Tips', content: 'Here are some tips for USMLE Step 1...', author: 'Dr<PERSON> <PERSON>', date: '2023-05-01', category: 'USMLE' },
  { id: '2', title: 'IMG Journey to Residency', content: 'My journey as an IMG to residency...', author: 'Dr<PERSON>', date: '2023-05-15', category: 'IMG' },
];

const services: Service[] = [
  { id: '1', name: 'USMLE Tutoring', description: 'One-on-one tutoring for USMLE exams', price: 100 },
  { id: '2', name: 'Residency Application Review', description: 'Comprehensive review of your residency application', price: 200 },
];

// Routes
app.get('/api/blog-posts', (req: Request, res: Response) => {
  res.json(blogPosts);
});

app.get('/api/services', (req: Request, res: Response) => {
  res.json(services);
});

app.post('/api/subscribe', (req: Request, res: Response) => {
  const { email, name }: SubscribeFormData = req.body;
  // Here you would typically save this to a database
  console.log(`New subscription: ${name} (${email})`);
  res.json({ message: 'Subscription successful' });
});

app.post('/api/study-partner', (req: Request, res: Response) => {
  const newProfile: StudyPartnerProfile = req.body;

  // Basic validation
  if (!newProfile.studyTimes || !newProfile.topicsOfInterest || !newProfile.examDate) {
    return res.status(400).json({ error: 'Missing required fields' });
  }

  // Generate a unique ID (in a real app, this would be handled by the database)
  const id = (fakeStudyPartners.length + 1).toString();
  const profileWithId = { ...newProfile, id };

  // Add the new profile to our fake data
  fakeStudyPartners.push(profileWithId);

  console.log('New study partner profile:', profileWithId);
  res.status(201).json({ message: 'Study partner profile created successfully', profile: profileWithId });
});

app.get('/api/study-partners', (req: Request, res: Response) => {
  res.json(fakeStudyPartners);
});

// --- Booking System API Endpoints ---

// Availability Rules
app.post('/api/availability', async (req: Request, res: Response) => {
  console.log('POST /api/availability hit');
  try {
    // TODO: Add input validation for req.body to ensure it matches Omit<AvailabilityRule, 'id' | 'created_at' | 'updated_at'>
    const newRule = await bookingService.addAvailabilityRule(req.body);
    res.status(201).json(newRule);
  } catch (error: any) {
    console.error('Error in POST /api/availability:', error);
    if (error.message.includes('Database error')) { // Check for specific error types if possible
        res.status(500).json({ message: 'Failed to add availability rule due to a database error.' });
    } else {
        res.status(400).json({ message: 'Failed to add availability rule.', error: error.message });
    }
  }
});

app.get('/api/availability', async (req: Request, res: Response) => {
  console.log('GET /api/availability hit');
  try {
    const rules = await bookingService.getAvailabilityRules();
    res.status(200).json(rules);
  } catch (error: any) {
    console.error('Error in GET /api/availability:', error);
    res.status(500).json({ message: 'Failed to retrieve availability rules.', error: error.message });
  }
});

app.delete('/api/availability/:ruleId', async (req: Request, res: Response) => {
  const { ruleId } = req.params;
  console.log(`DELETE /api/availability/${ruleId} hit`);
  try {
    const id = parseInt(ruleId, 10);
    if (isNaN(id)) {
      return res.status(400).json({ message: 'Invalid rule ID format.' });
    }
    const deleted = await bookingService.deleteAvailabilityRule(id);
    if (deleted) {
      res.status(200).json({ message: `Availability rule with ID ${id} deleted successfully.` });
      // Alternatively, use 204 No Content:
      // res.status(204).send();
    } else {
      res.status(404).json({ message: `Availability rule with ID ${id} not found.` });
    }
  } catch (error: any) {
    console.error(`Error in DELETE /api/availability/${ruleId}:`, error);
    res.status(500).json({ message: 'Failed to delete availability rule.', error: error.message });
  }
});

// Available Slots
app.get('/api/available-slots', async (req: Request, res: Response) => {
  console.log('GET /api/available-slots hit');
  const { date_start, date_end, slot_duration_minutes } = req.query;
  try {
    if (!date_start || !date_end) {
      return res.status(400).json({ message: 'Missing required query parameters: date_start and date_end.' });
    }

    const startDate = new Date(date_start as string);
    const endDate = new Date(date_end as string);
    const slotDuration = slot_duration_minutes ? parseInt(slot_duration_minutes as string, 10) : 60; // Default to 60 mins

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime())) {
        return res.status(400).json({ message: 'Invalid date format for date_start or date_end.' });
    }
    if (endDate < startDate) {
        return res.status(400).json({ message: 'date_end cannot be before date_start.' });
    }
    if (isNaN(slotDuration) || slotDuration <= 0) {
        return res.status(400).json({ message: 'slot_duration_minutes must be a positive integer.'});
    }

    const slots = await bookingService.getAvailableSlots(startDate, endDate, slotDuration);
    res.status(200).json(slots);
  } catch (error: any) {
    console.error('Error in GET /api/available-slots:', error);
    if (error.message.startsWith("Invalid start or end date") || error.message.startsWith("Slot duration must be")) {
        res.status(400).json({ message: error.message });
    } else {
        res.status(500).json({ message: 'Failed to get available slots.', error: error.message });
    }
  }
});

// Bookings
app.post('/api/bookings', async (req: Request, res: Response) => {
  console.log('POST /api/bookings hit');
  try {
    // Destructure all expected fields, including those for email personalization
    const { user_id, start_time, end_time, title, notes, client_name, client_email } = req.body;

    // Basic validation for email parameters
    if (!client_name || !client_email) {
      // Log this, but don't necessarily fail the booking if email is not critical path
      // However, for user confirmation, client_email is essential.
      console.warn('Client name or email not provided in request for booking notification.');
      // Depending on requirements, you might return a 400 error here:
      // return res.status(400).json({ message: 'Client name and email are required for booking notifications.' });
    }

    const bookingDBSchema = {
        user_id,
        start_time: start_time ? new Date(start_time) : undefined,
        end_time: end_time ? new Date(end_time) : undefined,
        title,
        notes
    };

    if (!bookingDBSchema.start_time || !bookingDBSchema.end_time || isNaN(bookingDBSchema.start_time.getTime()) || isNaN(bookingDBSchema.end_time.getTime())) {
        return res.status(400).json({ message: 'Invalid or missing start_time or end_time.' });
    }
     if (!bookingDBSchema.user_id) { // user_id is part of DB schema
        return res.status(400).json({ message: 'Missing user_id.' });
    }

    // Pass client_name and client_email to the service function
    const newBooking = await bookingService.createBooking(
        bookingDBSchema as any, // Cast for Omit type, after Date conversion
        client_name,
        client_email
    );
    res.status(201).json(newBooking);
  } catch (error: any) {
    console.error('Error in POST /api/bookings:', error.message);
    if (error.message === 'Time slot is already booked.') {
        res.status(409).json({ message: error.message });
    } else if (error.message.startsWith('Missing required fields') ||
               error.message.startsWith('Invalid date format') ||
               error.message.startsWith('End time must be after start time')) {
        res.status(400).json({ message: error.message });
    } else {
        res.status(500).json({ message: 'Failed to create booking.', error: error.message });
    }
  }
});

app.get('/api/bookings', async (req: Request, res: Response) => {
  console.log('GET /api/bookings hit (admin view)');
  const { start_date, end_date } = req.query;
  try {
    const startDate = start_date ? new Date(start_date as string) : undefined;
    const endDate = end_date ? new Date(end_date as string) : undefined;

    if (startDate && isNaN(startDate.getTime())) {
        return res.status(400).json({ message: 'Invalid start_date format.' });
    }
    if (endDate && isNaN(endDate.getTime())) {
        return res.status(400).json({ message: 'Invalid end_date format.' });
    }

    const bookings = await bookingService.getBookings(startDate, endDate);
    res.status(200).json(bookings);
  } catch (error: any) {
    console.error('Error in GET /api/bookings:', error);
    res.status(500).json({ message: 'Failed to retrieve bookings.', error: error.message });
  }
});


app.listen(port, () => {
  console.log(`Server is running on port ${port}`);
  // Test DB connection on startup, already called within db/index.ts
  // You could add other startup logs or checks here
});
