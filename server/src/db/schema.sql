-- Users table to hold basic user information
CREATE TABLE Users (
    id SERIAL PRIMARY KEY,
    name <PERSON><PERSON><PERSON><PERSON>(255) NOT NULL,
    email VARCHAR(255) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL, -- Store hashed passwords, not plain text
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

-- Availability table for defining available time slots or rules
CREATE TABLE Availability (
    id SERIAL PRIMARY KEY,
    rule_type VARCHAR(20) NOT NULL CHECK (rule_type IN ('recurring', 'specific_date')), -- e.g., 'recurring', 'specific_date'
    day_of_week VARCHAR(10) NULL, -- e.g., 'Monday', 'Tuesday', NULL for specific_date
    specific_date DATE NULL,      -- Specific date for an override or one-time availability
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_unavailable BOOLEAN DEFAULT FALSE, -- Marks the time slot as unavailable
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP
);

CREATE TABLE Bookings (
    id SERIAL PRIMARY KEY,
    user_id INTEGER NOT NULL, -- Assuming a user management system exists
    start_time TIMESTAMP WITH TIME ZONE NOT NULL,
    end_time TIMESTAMP WITH TIME ZONE NOT NULL,
    title VARCHAR(255) NULL,
    notes TEXT NULL,
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP,
    CONSTRAINT fk_user FOREIGN KEY (user_id) REFERENCES Users(id) -- Example FK, adjust if Users table is different
);

-- Optional: Add an index for faster lookups on Bookings.start_time and Bookings.end_time
CREATE INDEX idx_bookings_start_time ON Bookings(start_time);
CREATE INDEX idx_bookings_end_time ON Bookings(end_time);

-- Optional: Add an index for faster lookups on Availability
CREATE INDEX idx_availability_rule_type_specific_date ON Availability(rule_type, specific_date);
CREATE INDEX idx_availability_day_of_week ON Availability(day_of_week);
