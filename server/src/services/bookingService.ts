import { AvailabilityRule, Booking, TimeSlot } from '../types/booking';
import { query } from '../db'; // Import the query function
import {
    sendEmail,
    generateUserConfirmationEmail,
    generateAdminNotificationEmail,
    ADMIN_EMAIL
} from './emailService'; // Import email functions

/**
 * Retrieves available booking slots within a given date range and slot duration.
 *
 * @param startDate The start date of the range to check for availability.
 * @param endDate The end date of the range to check for availability.
 * @param slotDurationMinutes The duration of each desired slot in minutes.
 * @returns A promise that resolves to an array of available TimeSlot objects.
 */
export async function getAvailableSlots(
    startDate: Date,
    endDate: Date,
    slotDurationMinutes: number
): Promise<TimeSlot[]> {
    const availableSlots: TimeSlot[] = [];

    if (isNaN(startDate.getTime()) || isNaN(endDate.getTime()) || endDate < startDate) {
        throw new Error("Invalid start or end date provided for getAvailableSlots.");
    }
    if (slotDurationMinutes <= 0) {
        throw new Error("Slot duration must be a positive number.");
    }

    try {
        // 1. Fetch all Availability rules
        const availabilityRulesSql = 'SELECT * FROM Availability;';
        const availabilityResult = await query(availabilityRulesSql);
        const allRules: AvailabilityRule[] = availabilityResult.rows;

        // 2. Fetch all relevant Bookings
        // Fetch bookings that overlap with the broader query range to ensure all conflicts are caught.
        // (new_start < query_end) AND (new_end > query_start)
        const bookingsSql = 'SELECT * FROM Bookings WHERE start_time < $1 AND end_time > $2;';
        // Make endDate inclusive by setting it to the end of the day
        const queryEndDateForBookings = new Date(endDate);
        queryEndDateForBookings.setHours(23, 59, 59, 999);

        const bookingsResult = await query(bookingsSql, [queryEndDateForBookings, startDate]);
        const existingBookings: Booking[] = bookingsResult.rows;

        // Helper to convert "HH:MM:SS" string to [hours, minutes]
        const parseTimeString = (timeStr: string): [number, number] => {
            const [hours, minutes] = timeStr.split(':').map(Number);
            return [hours, minutes];
        };

        // Helper to get day of the week as string compatible with AvailabilityRule.day_of_week
        const getDayString = (date: Date): AvailabilityRule['day_of_week'] => {
            const days = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            return days[date.getDay()] as AvailabilityRule['day_of_week'];
        };

        // 4. Loop through each day from startDate to endDate
        let currentDay = new Date(startDate);
        currentDay.setHours(0,0,0,0); // Normalize to start of day

        while (currentDay <= endDate) {
            const dayStr = getDayString(currentDay);
            const currentDateStr = currentDay.toISOString().split('T')[0];

            // Find applicable rules for currentDay
            let applicableRules = allRules.filter(rule => {
                if (rule.rule_type === 'specific_date' && rule.specific_date) {
                    // Ensure specific_date from DB (which might be just DATE) is compared correctly
                    const ruleDate = new Date(rule.specific_date);
                    // Normalize ruleDate to UTC to match toISOString() output without timezone issues
                    const ruleDateStr = new Date(ruleDate.getUTCFullYear(), ruleDate.getUTCMonth(), ruleDate.getUTCDate()).toISOString().split('T')[0];
                    return ruleDateStr === currentDateStr;
                }
                if (rule.rule_type === 'recurring') {
                    return rule.day_of_week === dayStr;
                }
                return false;
            });

            // Override with specific_date rules if any, otherwise use recurring.
            const specificRules = applicableRules.filter(r => r.rule_type === 'specific_date');
            if (specificRules.length > 0) {
                applicableRules = specificRules;
            } else {
                applicableRules = applicableRules.filter(r => r.rule_type === 'recurring');
            }

            // If a specific rule marks the day as unavailable, or no rules apply, skip.
            if (applicableRules.some(r => r.is_unavailable && r.rule_type === 'specific_date')) {
                currentDay.setDate(currentDay.getDate() + 1);
                continue;
            }
            if (applicableRules.length === 0) {
                 currentDay.setDate(currentDay.getDate() + 1);
                continue;
            }

            // For simplicity, this MVP assumes one primary availability window per day from rules.
            // More complex scenarios might involve multiple available periods or merging overlapping rules.
            // We will process all non-unavailable rules and generate slots within them.
            for (const rule of applicableRules) {
                if (rule.is_unavailable) continue; // Skip generating slots for unavailable general rules

                const [openHours, openMinutes] = parseTimeString(rule.start_time);
                const [closeHours, closeMinutes] = parseTimeString(rule.end_time);

                let slotStart = new Date(currentDay);
                slotStart.setHours(openHours, openMinutes, 0, 0);

                let slotEnd = new Date(slotStart);
                slotEnd.setMinutes(slotStart.getMinutes() + slotDurationMinutes);

                const dayCloseTime = new Date(currentDay);
                dayCloseTime.setHours(closeHours, closeMinutes, 0, 0);

                while (slotEnd <= dayCloseTime) {
                    let isSlotFree = true;

                    // Check for conflict with existing Bookings
                    for (const booking of existingBookings) {
                        const bookingStart = new Date(booking.start_time);
                        const bookingEnd = new Date(booking.end_time);
                        // Overlap: (slotStart < bookingEnd) AND (slotEnd > bookingStart)
                        if (slotStart < bookingEnd && slotEnd > bookingStart) {
                            isSlotFree = false;
                            break;
                        }
                    }
                    if (!isSlotFree) {
                        slotStart.setMinutes(slotStart.getMinutes() + slotDurationMinutes); // Move to next potential slot
                        slotEnd.setMinutes(slotEnd.getMinutes() + slotDurationMinutes);
                        continue;
                    }

                    // Check for conflict with 'is_unavailable' blocks from *all* rules (e.g. a recurring lunch break)
                    // This needs to check any rule that could make this specific slot unavailable.
                    for (const unavailabilityRule of allRules) {
                        if (!unavailabilityRule.is_unavailable) continue;

                        // Check if unavailabilityRule applies to currentDay
                        let appliesToCurrentDay = false;
                        if (unavailabilityRule.rule_type === 'specific_date' && unavailabilityRule.specific_date) {
                             const ruleDate = new Date(unavailabilityRule.specific_date);
                             const ruleDateStr = new Date(ruleDate.getUTCFullYear(), ruleDate.getUTCMonth(), ruleDate.getUTCDate()).toISOString().split('T')[0];
                             if(ruleDateStr === currentDateStr) appliesToCurrentDay = true;
                        } else if (unavailabilityRule.rule_type === 'recurring' && unavailabilityRule.day_of_week === dayStr) {
                            appliesToCurrentDay = true;
                        }

                        if (appliesToCurrentDay) {
                            const [unavailStartHours, unavailStartMinutes] = parseTimeString(unavailabilityRule.start_time);
                            const [unavailEndHours, unavailEndMinutes] = parseTimeString(unavailabilityRule.end_time);

                            let unavailStart = new Date(currentDay);
                            unavailStart.setHours(unavailStartHours, unavailStartMinutes, 0, 0);
                            let unavailEnd = new Date(currentDay);
                            unavailEnd.setHours(unavailEndHours, unavailEndMinutes, 0, 0);

                            if (slotStart < unavailEnd && slotEnd > unavailStart) {
                                isSlotFree = false;
                                break;
                            }
                        }
                    }

                    if (isSlotFree) {
                        availableSlots.push({ startTime: new Date(slotStart), endTime: new Date(slotEnd) });
                    }

                    slotStart.setMinutes(slotStart.getMinutes() + slotDurationMinutes);
                    slotEnd.setMinutes(slotEnd.getMinutes() + slotDurationMinutes);
                }
            }
            currentDay.setDate(currentDay.getDate() + 1); // Move to next day
        }
        return availableSlots;
    } catch (error: any) {
        console.error('Error in getAvailableSlots:', error);
        throw new Error(`Failed to get available slots: ${error.message}`);
    }
}

/**
 * Adds a new availability rule to the system.
 * @param rule The availability rule to add (excluding id, created_at, updated_at).
 * @returns A promise that resolves to the created AvailabilityRule with its new ID and timestamps.
 */
export async function addAvailabilityRule(rule: Omit<AvailabilityRule, 'id' | 'created_at' | 'updated_at'>): Promise<AvailabilityRule> {
    const {
        rule_type,
        day_of_week,
        specific_date,
        start_time,
        end_time,
        is_unavailable
    } = rule;

    // Ensure day_of_week is null if rule_type is 'specific_date' and specific_date is null if rule_type is 'recurring'
    const finalDayOfWeek = rule_type === 'specific_date' ? null : day_of_week;
    const finalSpecificDate = rule_type === 'recurring' ? null : specific_date;

    const sql = `
        INSERT INTO Availability (rule_type, day_of_week, specific_date, start_time, end_time, is_unavailable)
        VALUES ($1, $2, $3, $4, $5, $6)
        RETURNING *;
    `;
    const params = [
        rule_type,
        finalDayOfWeek,
        finalSpecificDate,
        start_time,
        end_time,
        is_unavailable === undefined ? false : is_unavailable // Default is_unavailable to false
    ];

    try {
        const result = await query(sql, params);
        if (result.rows.length > 0) {
            return result.rows[0] as AvailabilityRule;
        } else {
            throw new Error('Failed to create availability rule: No rows returned.');
        }
    } catch (error) {
        console.error('Error adding availability rule:', error);
        // Consider more specific error types or messages based on db error codes
        throw new Error('Database error while adding availability rule.');
    }
}

/**
 * Retrieves all availability rules from the system.
 * @returns A promise that resolves to an array of all AvailabilityRule objects.
 */
export async function getAvailabilityRules(): Promise<AvailabilityRule[]> {
    const sql = 'SELECT * FROM Availability ORDER BY created_at DESC;'; // Or by other relevant fields
    try {
        const result = await query(sql);
        return result.rows as AvailabilityRule[];
    } catch (error) {
        console.error('Error retrieving availability rules:', error);
        throw new Error('Database error while retrieving availability rules.');
    }
}

/**
 * Deletes an availability rule from the system.
 * @param ruleId The ID of the availability rule to delete.
 * @returns A promise that resolves when the rule has been deleted (true if deleted, false if not found).
 */
export async function deleteAvailabilityRule(ruleId: number): Promise<boolean> {
    const sql = 'DELETE FROM Availability WHERE id = $1 RETURNING id;';
    try {
        const result = await query(sql, [ruleId]);
        return result.rowCount > 0; // rowCount indicates how many rows were deleted
    } catch (error) {
        console.error(`Error deleting availability rule with ID ${ruleId}:`, error);
        throw new Error('Database error while deleting availability rule.');
    }
}

/**
 * Creates a new booking in the system.
 * @param bookingDetails The details of the booking to create (excluding id, created_at, updated_at).
 * @returns A promise that resolves to the created Booking object with its new ID and timestamps.
 */
export async function createBooking(
    bookingDetails: Omit<Booking, 'id' | 'created_at' | 'updated_at'>,
    clientName?: string, // Optional: client's name for email
    clientEmail?: string // Optional: client's email for sending confirmation
): Promise<Booking> {
    const { user_id, start_time, end_time, title, notes } = bookingDetails;

    // Validation
    if (!user_id || !start_time || !end_time) {
        throw new Error('Missing required fields: user_id, start_time, or end_time.');
    }

    const startTimeDate = new Date(start_time);
    const endTimeDate = new Date(end_time);

    if (isNaN(startTimeDate.getTime()) || isNaN(endTimeDate.getTime())) {
        throw new Error('Invalid date format for start_time or end_time.');
    }

    if (endTimeDate <= startTimeDate) {
        throw new Error('End time must be after start time.');
    }

    // Conflict Check
    // An overlap occurs if (new_start < existing_end) AND (new_end > existing_start)
    const conflictCheckSql = `
        SELECT id FROM Bookings
        WHERE ($1 < end_time) AND ($2 > start_time);
    `;
    const conflictCheckParams = [startTimeDate, endTimeDate];

    try {
        const conflictResult = await query(conflictCheckSql, conflictCheckParams);
        if (conflictResult.rows.length > 0) {
            throw new Error('Time slot is already booked.');
        }

        // Insert new booking
        const insertSql = `
            INSERT INTO Bookings (user_id, start_time, end_time, title, notes)
            VALUES ($1, $2, $3, $4, $5)
            RETURNING *;
        `;
        const insertParams = [user_id, startTimeDate, endTimeDate, title, notes];

        const result = await query(insertSql, insertParams);
        if (result.rows.length > 0) {
            const newBooking = result.rows[0] as Booking;

            // Send emails (fire and forget)
            if (clientEmail && clientName) { // Only send if email and name are provided
                try {
                    const userEmailContent = generateUserConfirmationEmail(newBooking, clientName, clientEmail);
                    sendEmail(clientEmail, userEmailContent.subject, userEmailContent.html, userEmailContent.text);

                    const adminEmailContent = generateAdminNotificationEmail(newBooking, clientName, clientEmail);
                    sendEmail(ADMIN_EMAIL, adminEmailContent.subject, adminEmailContent.html, adminEmailContent.text);
                } catch (emailError) {
                    console.error("Error preparing or queueing emails:", emailError);
                    // Log but don't fail booking
                }
            } else {
                console.warn("Client name or email not provided for booking confirmation email. Skipping email notifications.");
            }

            return newBooking;
        } else {
            // This case should ideally not be reached if RETURNING * is used and insert is successful
            throw new Error('Failed to create booking: No rows returned.');
        }
    } catch (error: any) {
        console.error('Error creating booking:', error.message);
        // Re-throw the original error if it's one of our specific validation/conflict errors
        if (error.message.startsWith('Missing required fields') ||
            error.message.startsWith('Invalid date format') ||
            error.message.startsWith('End time must be after start time') ||
            error.message.startsWith('Time slot is already booked')) {
            throw error;
        }
        // Otherwise, wrap it as a generic database error
        throw new Error(`Database error while creating booking: ${error.message}`);
    }
}

/**
 * Retrieves bookings from the system, optionally filtered by a date range.
 * @param startDate Optional start date to filter bookings.
 * @param endDate Optional end date to filter bookings.
 * @returns A promise that resolves to an array of Booking objects.
 */
export async function getBookings(startDate?: Date, endDate?: Date): Promise<Booking[]> {
    let sql = 'SELECT * FROM Bookings';
    const params: any[] = [];
    const conditions: string[] = [];

    if (startDate) {
        params.push(startDate);
        conditions.push(`start_time >= $${params.length}`);
    }
    if (endDate) {
        params.push(endDate);
        // To include bookings that end on the endDate, we might need to adjust the comparison
        // e.g., end_time <= specific_time_on_endDate or start_time < (endDate + 1 day)
        // For simplicity, using end_time <= endDate. Adjust as per exact requirements.
        conditions.push(`end_time <= $${params.length}`);
    }

    if (conditions.length > 0) {
        sql += ' WHERE ' + conditions.join(' AND ');
    }

    sql += ' ORDER BY start_time ASC;'; // Order by start time

    try {
        const result = await query(sql, params);
        return result.rows as Booking[];
    } catch (error: any) {
        console.error('Error retrieving bookings:', error);
        throw new Error(`Database error while retrieving bookings: ${error.message}`);
    }
}
