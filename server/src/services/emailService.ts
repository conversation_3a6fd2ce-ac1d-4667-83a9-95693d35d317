import nodemailer from 'nodemailer';
import { Booking } from '../types/booking'; // Assuming Booking type is available

// Define ADMIN_EMAIL, prioritizing environment variable
export const ADMIN_EMAIL = process.env.ADMIN_EMAIL_ADDRESS || '<EMAIL>';
const EMAIL_FROM_ADDRESS = process.env.EMAIL_FROM || `"UniversalMD Booking" <<EMAIL>>`;

let transporter: nodemailer.Transporter;

// Async function to initialize transporter
async function initializeTransporter() {
    if (process.env.EMAIL_HOST && process.env.EMAIL_USER && process.env.EMAIL_PASS) {
        transporter = nodemailer.createTransport({
            host: process.env.EMAIL_HOST,
            port: parseInt(process.env.EMAIL_PORT || '587', 10),
            secure: (process.env.EMAIL_SECURE === 'true'), // true for 465, false for other ports
            auth: {
                user: process.env.EMAIL_USER,
                pass: process.env.EMAIL_PASS,
            },
            tls: {
                // do not fail on invalid certs if using self-signed certs in dev
                rejectUnauthorized: process.env.NODE_ENV === 'production',
            }
        });
        try {
            await transporter.verify();
            console.log('SMTP transporter configured and verified successfully.');
        } catch (error) {
            console.error('SMTP transporter configuration error:', error);
            // Fallback to Ethereal if SMTP verification fails
            await configureEthereal();
        }
    } else {
        await configureEthereal();
    }
}

async function configureEthereal() {
    try {
        const testAccount = await nodemailer.createTestAccount();
        console.log('Ethereal test account created for development email sending.');
        console.log('Ethereal User:', testAccount.user);
        console.log('Ethereal Pass:', testAccount.pass);

        transporter = nodemailer.createTransport({
            host: 'smtp.ethereal.email',
            port: 587,
            secure: false,
            auth: {
                user: testAccount.user,
                pass: testAccount.pass,
            },
        });
        // No need to log preview URL here, it's per message
    } catch (err) {
        console.error('Failed to create Ethereal test account:', err);
        // If Ethereal fails, email sending will not work.
        // Consider a more robust fallback or error handling for critical email scenarios.
    }
}


// Call initializeTransporter when the module is loaded.
// Top-level await is generally available in modern Node.js modules.
// If issues arise, this might need to be called explicitly at app startup.
initializeTransporter().catch(console.error);


export async function sendEmail(to: string, subject: string, html: string, text?: string) {
    if (!transporter) {
        console.error('Email transporter not initialized. Email will not be sent.');
        // Attempt to initialize it again in case it failed silently or was deferred
        // await initializeTransporter();
        // if (!transporter) { // check again
        //    console.error('Re-initialization failed. Email not sent.');
        //    return;
        // }
        // For now, let's not re-initialize here to avoid complexity in this step.
        // It should be initialized at startup.
        return;
    }

    try {
        const info = await transporter.sendMail({
            from: EMAIL_FROM_ADDRESS,
            to,
            subject,
            text,
            html,
        });

        console.log('Message sent: %s', info.messageId);
        // Log preview URL if using Ethereal
        if (transporter.options && (transporter.options as any).host === 'smtp.ethereal.email') {
            console.log('Preview URL (Ethereal): %s', nodemailer.getTestMessageUrl(info));
        }
    } catch (error) {
        console.error('Error sending email:', error);
        // Do not throw error here to prevent impacting other operations like booking success response
    }
}

// Email Template Functions
export function generateUserConfirmationEmail(booking: Booking, userName?: string, userEmail?: string): { subject: string; html: string; text: string } {
    const bookingStartTime = new Date(booking.start_time).toLocaleString([], { dateStyle: 'full', timeStyle: 'short'});
    const bookingEndTime = new Date(booking.end_time).toLocaleString([], {timeStyle: 'short'});
    // booking.user_id is just an ID. We need actual name/email if not on the booking object directly.
    // For now, assuming booking object might have user_name/user_email or they are passed in.
    // The current Booking type doesn't have user_email, so it needs to be passed or fetched.
    // Let's assume userName and userEmail are passed if not directly on booking.
    // For the API, the `createBooking` in `bookingService` gets `user_id`, not email or name directly.
    // This is a gap. For now, use a placeholder if not available. The `createBooking` in `index.ts` gets `req.body` which might have it.
    // Let's assume `booking.title` might contain user's name for now, or use passed `userName`.

    const finalUserName = userName || (booking as any).user_name || 'Client'; // Adjust based on actual data flow

    const subject = 'Your Consultation Booking Confirmation';
    const text = `Dear ${finalUserName},\n\nYour consultation has been successfully booked.\n\nDetails:\nService: ${booking.title || 'Consultation'}\nTime: ${bookingStartTime} - ${bookingEndTime}\n\nWe look forward to speaking with you!\n\nSincerely,\nThe UniversalMD Team`;
    const html = `
        <p>Dear ${finalUserName},</p>
        <p>Your consultation has been successfully booked.</p>
        <h3>Booking Details:</h3>
        <ul>
            <li><strong>Service:</strong> ${booking.title || 'Consultation'}</li>
            <li><strong>Time:</strong> ${bookingStartTime} - ${bookingEndTime}</li>
            ${booking.notes ? `<li><strong>Notes:</strong> ${booking.notes}</li>` : ''}
        </ul>
        <p>We look forward to speaking with you!</p>
        <p>Sincerely,<br/>The UniversalMD Team</p>
    `;
    return { subject, html, text };
}

export function generateAdminNotificationEmail(booking: Booking, clientName?: string, clientEmail?: string): { subject: string; html: string; text: string } {
    const bookingStartTime = new Date(booking.start_time).toLocaleString([], { dateStyle: 'full', timeStyle: 'short'});
    const bookingEndTime = new Date(booking.end_time).toLocaleString([], {timeStyle: 'short'});

    // Similar to user email, clientName and clientEmail might need to be explicitly passed
    // or retrieved if not directly on the booking object.
    const finalClientName = clientName || (booking as any).user_name || 'A client';
    const finalClientEmail = clientEmail || (booking as any).user_email || 'Not provided';


    const subject = `New Consultation Booking: ${finalClientName} on ${new Date(booking.start_time).toLocaleDateString()}`;
    const text = `A new consultation has been booked.\n\nClient: ${finalClientName}\nEmail: ${finalClientEmail}\nService: ${booking.title || 'Consultation'}\nTime: ${bookingStartTime} - ${bookingEndTime}\n${booking.notes ? `Notes: ${booking.notes}\n` : ''}\nPlease check the admin panel for more details.`;
    const html = `
        <p>A new consultation has been booked.</p>
        <h3>Booking Details:</h3>
        <ul>
            <li><strong>Client Name:</strong> ${finalClientName}</li>
            <li><strong>Client Email:</strong> ${finalClientEmail}</li>
            <li><strong>Service:</strong> ${booking.title || 'Consultation'}</li>
            <li><strong>Time:</strong> ${bookingStartTime} - ${bookingEndTime}</li>
            ${booking.notes ? `<li><strong>Notes:</strong> ${booking.notes}</li>` : ''}
        </ul>
        <p>Please check the admin panel for more details.</p>
    `;
    return { subject, html, text };
}
