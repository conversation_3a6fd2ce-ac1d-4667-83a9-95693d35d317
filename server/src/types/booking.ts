export interface AvailabilityRule {
    id: number;
    rule_type: 'recurring' | 'specific_date';
    day_of_week?: 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | 'Sunday' | null;
    specific_date?: Date | null;
    start_time: string; // Represent time as string e.g., "09:00:00"
    end_time: string;   // Represent time as string e.g., "17:00:00"
    is_unavailable?: boolean;
    created_at: Date;
    updated_at: Date;
}

export interface Booking {
    id: number;
    user_id: number;
    start_time: Date; // Full timestamp
    end_time: Date;   // Full timestamp
    title?: string | null;
    notes?: string | null;
    created_at: Date;
    updated_at: Date;
}

export interface TimeSlot {
    startTime: Date;
    endTime: Date;
}
