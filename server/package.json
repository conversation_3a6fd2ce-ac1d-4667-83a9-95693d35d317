{"name": "imgdreamsupport-server", "version": "1.0.0", "description": "Server for IMG Dream Support", "main": "dist/index.js", "scripts": {"build": "tsc", "start": "node dist/index.js", "dev": "ts-node-dev --respawn --transpile-only src/index.ts"}, "dependencies": {"cors": "^2.8.5", "express": "^4.17.1", "imgdreamsupport-platform": "file:../platform", "pg": "^8.7.1", "nodemailer": "^6.7.2"}, "devDependencies": {"@types/cors": "^2.8.17", "@types/express": "^4.17.21", "@types/pg": "^8.6.3", "@types/nodemailer": "^6.4.4", "@types/node": "^16.11.12", "nodemon": "^2.0.7", "ts-node-dev": "^1.1.8", "typescript": "^4.5.4"}}