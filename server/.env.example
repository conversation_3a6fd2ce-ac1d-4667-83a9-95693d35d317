# Nodemailer SMTP Configuration
EMAIL_HOST=smtp.example.com
EMAIL_PORT=587
EMAIL_SECURE=false # true for port 465, false for others (like 587 with STARTTLS)
EMAIL_USER=<EMAIL>
EMAIL_PASS=yourpassword
EMAIL_FROM="UniversalMD Booking" <<EMAIL>> # Sender name and address
ADMIN_EMAIL_ADDRESS=<EMAIL> # For receiving booking notifications

# PostgreSQL Connection (already used by db/index.ts, good to document all env vars)
# These are the default names used by the Pool if not set, but can be overridden.
# PGUSER=postgres
# PGHOST=localhost
# PGDATABASE=booking_system_dev
# PGPASSWORD=localdevpassword # Example password from db/index.ts default
# PGPORT=5432

# Application Environment
# NODE_ENV=development # or production
# PORT=5000 # Server port, already defaulted in index.ts
