import React from 'react';
import {motion} from 'framer-motion';
import {Button} from '../components/ui/button';
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from '../components/ui/card';
import {CheckCir<PERSON>, Star} from 'lucide-react';

interface Package {
	id: number;
	name: string;
	description: string;
	price: number;
	originalPrice: number;
	features: string[];
	popular?: boolean;
	tag?: string;
}

const packages: Package[] = [
	{
		id: 1,
		name: 'Essential Prep',
		description: 'Perfect for IMGs starting their journey',
		price: 999,
		originalPrice: 1299,
		features: ['MCCQE Online Course', 'Basic Question Bank Access', '2 Mock Exams', 'Study Planning Guide', 'Email Support'],
	},
	{
		id: 2,
		name: 'Professional Package',
		description: 'Most popular choice for comprehensive preparation',
		price: 1999,
		originalPrice: 2499,
		popular: true,
		tag: 'MOST POPULAR',
		features: [
			'MCCQE & NAC-OSCE Exam Prep Course',
			'2 One-on-One Coaching Sessions',
			'Interview Prep Workshop',
			'Priority Email & Chat Support',
			'Study Partner Matching',
		],
	},
	{
		id: 3,
		name: 'Ultimate Success',
		description: 'Complete preparation for guaranteed success',
		price: 2999,
		originalPrice: 3999,
		tag: 'BEST VALUE',
		features: [
			'All Professional Package Features',
			'Unlimited Mock Exams',
			'Personal Success Coach',
			'4 Additional Coaching Sessions',
			'MMI Interview Course',
			'Application Review Service',
			'CV & Personal Statement Review',
			'24/7 Priority Support',
			'Lifetime Access to Materials',
		],
	},
];

const Packages = () => {
	return (
		<div className='min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-slate-900 dark:to-slate-800'>
			{/* Hero Section */}
			<section className='relative py-20 bg-gradient-to-r from-purple-50 to-red-50 dark:from-purple-900 dark:to-red-900'>
				<div className='container mx-auto px-4'>
					<motion.div
						className='max-w-3xl mx-auto text-center'
						initial={{opacity: 0, y: 20}}
						animate={{opacity: 1, y: 0}}
						transition={{duration: 0.5}}>
						<h1 className='text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white'>Choose Your Success Package</h1>
						<p className='text-xl text-gray-600 dark:text-gray-300 mb-8'>Comprehensive packages designed to maximize your chances of success</p>
					</motion.div>
				</div>
			</section>

			{/* Packages Grid */}
			<section className='py-20'>
				<div className='container mx-auto px-4'>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-7xl mx-auto'>
						{packages.map((pkg, index) => (
							<motion.div
								key={pkg.id}
								initial={{opacity: 0, y: 20}}
								animate={{opacity: 1, y: 0}}
								transition={{delay: index * 0.1}}
								className='relative'>
								{pkg.tag && (
									<div className='absolute -top-4 left-1/2 transform -translate-x-1/2 bg-red-700 dark:bg-red-600 text-white px-4 py-1 rounded-full text-sm font-bold z-10'>
										{pkg.tag}
									</div>
								)}
								<Card
									className={`h-full hover:shadow-xl transition-shadow ${pkg.popular ? 'border-red-700 dark:border-red-500 shadow-lg' : 'dark:border-gray-700'} bg-white dark:bg-gray-800`}>
									<CardHeader>
										<CardTitle className='text-2xl text-gray-900 dark:text-white'>{pkg.name}</CardTitle>
										<CardDescription className='text-gray-600 dark:text-gray-300'>{pkg.description}</CardDescription>
									</CardHeader>
									<CardContent>
										<div className='mb-6'>
											<span className='text-4xl font-bold text-red-700 dark:text-red-500'>${pkg.price}</span>
											<span className='text-gray-500 dark:text-gray-400 line-through ml-2'>${pkg.originalPrice}</span>
											<span className='text-green-600 dark:text-green-500 ml-2'>Save ${pkg.originalPrice - pkg.price}</span>
										</div>
										<ul className='space-y-3'>
											{pkg.features.map((feature, featureIndex) => (
												<motion.li
													key={featureIndex}
													initial={{opacity: 0, x: -20}}
													animate={{opacity: 1, x: 0}}
													transition={{delay: index * 0.1 + featureIndex * 0.05}}
													className='flex items-start gap-2'>
													<CheckCircle className='w-5 h-5 text-green-600 dark:text-green-500 mt-0.5 flex-shrink-0' />
													<span class='text-gray-700 dark:text-gray-300'>{feature}</span>
												</motion.li>
											))}
										</ul>
									</CardContent>
									<CardFooter>
										<Button
											className={`w-full ${pkg.popular ? 'bg-red-700 hover:bg-red-800 dark:bg-red-600 dark:hover:bg-red-700' : 'bg-gray-700 hover:bg-gray-800 dark:bg-gray-600 dark:hover:bg-gray-700'} text-white`}>
											Choose {pkg.name}
										</Button>
									</CardFooter>
								</Card>
							</motion.div>
						))}
					</div>
				</div>
			</section>

			{/* Features Section */}
			<section className='py-20 bg-gray-50 dark:bg-slate-800'>
				<div className='container mx-auto px-4'>
					<motion.div className='max-w-3xl mx-auto text-center mb-12' initial={{opacity: 0}} animate={{opacity: 1}}>
						<h2 className='text-3xl font-bold mb-4 text-gray-900 dark:text-white'>All Packages Include</h2>
						<p className='text-gray-600 dark:text-gray-300'>Every package comes with these essential features to support your success</p>
					</motion.div>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 max-w-5xl mx-auto'>
						{[
							'Access to Online Learning Platform',
							'Study Materials & Resources',
							'Progress Tracking',
							'Mobile App Access',
							'Community Forum Access',
							'Certificate of Completion',
						].map((feature, index) => (
							<motion.div
								key={index}
								initial={{opacity: 0, y: 20}}
								animate={{opacity: 1, y: 0}}
								transition={{delay: index * 0.1}}
								className='bg-white dark:bg-gray-800 p-6 rounded-lg shadow-sm flex items-start gap-3 dark:border dark:border-gray-700'>
								<CheckCircle className='w-6 h-6 text-green-600 dark:text-green-500 flex-shrink-0' />
								<span className='text-gray-700 dark:text-gray-300'>{feature}</span>
							</motion.div>
						))}
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className='py-20 bg-red-700 dark:bg-red-800 text-white'>
				<div className='container mx-auto px-4'>
					<motion.div className='max-w-3xl mx-auto text-center' initial={{opacity: 0}} animate={{opacity: 1}} transition={{delay: 0.4}}>
						<h2 className='text-3xl font-bold mb-6 text-white'>Need Help Choosing?</h2>
						<p className='text-lg mb-8 text-red-100 dark:text-red-200'>
							Book a free consultation with our advisors to find the perfect package for your needs and goals.
						</p>
						<Button
							size='lg'
							variant='outline'
							className='bg-white text-red-700 hover:bg-red-50 dark:bg-gray-100 dark:text-red-600 dark:hover:bg-gray-200'>
							Schedule Free Consultation
						</Button>
					</motion.div>
				</div>
			</section>
		</div>
	);
};

export default Packages;
