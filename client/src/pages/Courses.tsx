import React, {useState} from 'react';
import {motion} from 'framer-motion';
import {<PERSON><PERSON>} from '../components/ui/button';
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from '../components/ui/card';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from '../components/ui/select';
import {Input} from '../components/ui/input';
import {Search, Clock, Users, Star, BookOpen} from 'lucide-react';

interface Course {
	id: number;
	title: string;
	category: string;
	description: string;
	duration: string;
	studentsCount: number;
	rating: number;
	price: number;
	level: 'Beginner' | 'Intermediate' | 'Advanced';
	image: string;
}

const courses: Course[] = [
	{
		id: 1,
		title: 'MCCQE Comprehensive Review',
		category: 'Exam Prep',
		description: 'Complete preparation for the Medical Council of Canada Qualifying Examination Part 1.',
		duration: '12 weeks',
		studentsCount: 1250,
		rating: 4.8,
		price: 899,
		level: 'Advanced',
		image: '/courses_image.jpeg',
	},
	{
		id: 2,
		title: 'NAC OSCE Mastery',
		category: 'Clinical Skills',
		description: 'Master clinical skills and scenarios for the NAC OSCE examination.',
		duration: '8 weeks',
		studentsCount: 850,
		rating: 4.9,
		price: 799,
		level: 'Advanced',
		image: '/consultation_image.jpeg',
	},
	{
		id: 3,
		title: 'MCCQE & NAC-OSCE Preparation',
		category: 'Exam Prep',
		description: 'Comprehensive preparation for the Computer-Based Assessment for Sampling Personal Characteristics.',
		duration: '4 weeks',
		studentsCount: 2100,
		rating: 4.7,
		price: 299,
		level: 'Intermediate',
		image: '/studypartner_image.jpeg',
	},
	{
		id: 4,
		title: 'Medical Communication Skills',
		category: 'Professional Development',
		description: 'Enhance your communication skills for better patient interactions and team collaboration.',
		duration: '6 weeks',
		studentsCount: 750,
		rating: 4.6,
		price: 399,
		level: 'Beginner',
		image: '/mockexam_image.jpeg',
	},
];

const Courses = () => {
	const [searchQuery, setSearchQuery] = useState('');
	const [selectedCategory, setSelectedCategory] = useState('all');
	const [selectedLevel, setSelectedLevel] = useState('all');

	const filteredCourses = courses.filter((course) => {
		const matchesSearch =
			course.title.toLowerCase().includes(searchQuery.toLowerCase()) || course.description.toLowerCase().includes(searchQuery.toLowerCase());
		const matchesCategory = selectedCategory === 'all' || course.category === selectedCategory;
		const matchesLevel = selectedLevel === 'all' || course.level === selectedLevel;
		return matchesSearch && matchesCategory && matchesLevel;
	});

	return (
		<div className='min-h-screen bg-gradient-to-b from-white to-gray-50 dark:from-slate-900 dark:to-slate-800'>
			{/* Hero Section */}
			<section className='relative py-20 bg-gradient-to-r from-blue-50 to-red-50 dark:from-blue-900 dark:to-red-900'>
				<div className='container mx-auto px-4'>
					<motion.div
						className='max-w-3xl mx-auto text-center'
						initial={{opacity: 0, y: 20}}
						animate={{opacity: 1, y: 0}}
						transition={{duration: 0.5}}>
						<h1 className='text-4xl md:text-5xl font-bold mb-6 text-gray-900 dark:text-white'>Professional Medical Courses</h1>
						<p className='text-xl text-gray-600 dark:text-gray-300 mb-8'>Advance your medical career with our comprehensive courses</p>
					</motion.div>
				</div>
			</section>

			{/* Filters Section */}
			<section className='py-8 bg-white dark:bg-gray-800 shadow-sm'>
				<div className='container mx-auto px-4'>
					<div className='flex flex-col md:flex-row gap-4 items-center justify-between'>
						<div className='relative flex-1 max-w-md'>
							<Search className='absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 dark:text-gray-500' />
							<Input
								placeholder='Search courses...'
								className='pl-10 bg-white dark:bg-gray-700 dark:text-white dark:border-gray-600'
								value={searchQuery}
								onChange={(e) => setSearchQuery(e.target.value)}
							/>
						</div>
						<div className='flex gap-4'>
							<Select onValueChange={setSelectedCategory}>
								<SelectTrigger className='w-[180px] bg-white dark:bg-gray-700 dark:text-white dark:border-gray-600'>
									<SelectValue placeholder='Category' />
								</SelectTrigger>
								<SelectContent className='bg-white dark:bg-gray-700 dark:text-white dark:border-gray-600'>
									<SelectItem value='all'>All Categories</SelectItem>
									<SelectItem value='Exam Prep'>Exam Prep</SelectItem>
									<SelectItem value='Clinical Skills'>Clinical Skills</SelectItem>
									<SelectItem value='Professional Development'>Professional Development</SelectItem>
								</SelectContent>
							</Select>
							<Select onValueChange={setSelectedLevel}>
								<SelectTrigger className='w-[180px] bg-white dark:bg-gray-700 dark:text-white dark:border-gray-600'>
									<SelectValue placeholder='Level' />
								</SelectTrigger>
								<SelectContent className='bg-white dark:bg-gray-700 dark:text-white dark:border-gray-600'>
									<SelectItem value='all'>All Levels</SelectItem>
									<SelectItem value='Beginner'>Beginner</SelectItem>
									<SelectItem value='Intermediate'>Intermediate</SelectItem>
									<SelectItem value='Advanced'>Advanced</SelectItem>
								</SelectContent>
							</Select>
						</div>
					</div>
				</div>
			</section>

			{/* Courses Grid */}
			<section className='py-20'>
				<div className='container mx-auto px-4'>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
						{filteredCourses.map((course, index) => (
							<motion.div key={course.id} initial={{opacity: 0, y: 20}} animate={{opacity: 1, y: 0}} transition={{delay: index * 0.1}}>
								<Card className='h-full hover:shadow-lg transition-shadow overflow-hidden bg-white dark:bg-gray-800 dark:border-gray-700'>
									<div className='relative h-48'>
										<img src={course.image} alt={course.title} className='w-full h-full object-cover' />
										<div className='absolute top-4 right-4 bg-white dark:bg-gray-700 px-3 py-1 rounded-full text-sm font-medium text-red-700 dark:text-red-400'>
											{course.category}
										</div>
									</div>
									<CardHeader>
										<CardTitle className='text-gray-900 dark:text-white'>{course.title}</CardTitle>
										<CardDescription className='text-gray-600 dark:text-gray-300'>{course.description}</CardDescription>
									</CardHeader>
									<CardContent>
										<div className='flex items-center gap-4 text-sm text-gray-600 dark:text-gray-400'>
											<div className='flex items-center gap-1'>
												<Clock className='w-4 h-4' />
												<span>{course.duration}</span>
											</div>
											<div className='flex items-center gap-1'>
												<Users className='w-4 h-4' />
												<span>{course.studentsCount} students</span>
											</div>
											<div className='flex items-center gap-1'>
												<Star className='w-4 h-4 text-yellow-400' />
												<span>{course.rating}</span>
											</div>
										</div>
									</CardContent>
									<CardFooter className='flex justify-between items-center'>
										<span className='text-2xl font-bold text-red-700 dark:text-red-500'>${course.price}</span>
										<Button className='bg-red-700 hover:bg-red-800 dark:bg-red-600 dark:hover:bg-red-700 text-white'>Enroll Now</Button>
									</CardFooter>
								</Card>
							</motion.div>
						))}
					</div>
				</div>
			</section>

			{/* CTA Section */}
			<section className='py-20 bg-red-700 dark:bg-red-800 text-white'>
				<div className='container mx-auto px-4'>
					<motion.div className='max-w-3xl mx-auto text-center' initial={{opacity: 0}} animate={{opacity: 1}} transition={{delay: 0.4}}>
						<h2 className='text-3xl font-bold mb-6 text-white'>Not Sure Where to Start?</h2>
						<p className='text-lg mb-8 text-red-100 dark:text-red-200'>
							Schedule a free consultation with our academic advisors to find the perfect course for your goals.
						</p>
						<Button
							size='lg'
							variant='outline'
							className='bg-white text-red-700 hover:bg-red-50 dark:bg-gray-100 dark:text-red-600 dark:hover:bg-gray-200'>
							Book Free Consultation
						</Button>
					</motion.div>
				</div>
			</section>
		</div>
	);
};

export default Courses;
