import React from 'react';
import AvailabilityForm from '../components/admin/AvailabilityForm';
import AvailabilityList from '../components/admin/AvailabilityList';
import { Separator } from '../components/ui/separator';

const AdminAvailabilityPage: React.FC = () => {
  return (
    <div className="container mx-auto py-8 px-4 md:px-6 lg:px-8">
      <header className="mb-8">
        <h1 className="text-3xl md:text-4xl font-bold tracking-tight text-gray-900 dark:text-gray-100">
          Admin Availability Management
        </h1>
        <p className="mt-2 text-lg text-gray-600 dark:text-gray-400">
          Define and manage recurring and specific date availability rules for bookings.
        </p>
      </header>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-8 items-start">
        <div className="lg:col-span-1">
          <AvailabilityForm />
        </div>
        <div className="lg:col-span-2">
          <AvailabilityList />
        </div>
      </div>

      <Separator className="my-12" />

      {/* Could add more sections here if needed, e.g., a calendar preview */}

    </div>
  );
};

export default AdminAvailabilityPage;
