import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '../components/ui/button';
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from '../components/ui/accordion';

const CasperPrep = () => {
  const features = [
    {
      title: 'Practice Scenarios',
      content: [
        {
          title: 'Realistic Scenarios',
          description: 'Practice with scenarios that mirror the actual CASPer test format.'
        },
        {
          title: 'Timed Practice',
          description: 'Get comfortable with the time constraints of the real exam.'
        },
        {
          title: 'Expert Feedback',
          description: 'Receive detailed feedback on your responses from experienced evaluators.'
        }
      ]
    },
    {
      title: 'Preparation Resources',
      content: [
        {
          title: 'Strategy Guide',
          description: 'Learn proven strategies for approaching different types of scenarios.'
        },
        {
          title: 'Sample Responses',
          description: 'Study example responses that demonstrate effective communication and reasoning.'
        },
        {
          title: 'Writing Tips',
          description: 'Master the art of clear, concise, and impactful responses.'
        }
      ]
    },
    {
      title: 'Live Training',
      content: [
        {
          title: 'Group Workshops',
          description: 'Interactive sessions focusing on key CASPer competencies.',
          price: 'From $199'
        },
        {
          title: 'One-on-One Coaching',
          description: 'Personalized guidance tailored to your needs.',
          price: 'From $299'
        },
        {
          title: 'Mock Tests',
          description: 'Full-length practice tests with comprehensive feedback.',
          price: 'From $149'
        }
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-red-50 to-red-100">
        <div className="container mx-auto px-4">
          <motion.div 
            className="max-w-3xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              CASPer® Test Preparation
            </h1>
            <p className="text-xl text-gray-600 mb-8">
              Comprehensive preparation for the Computer-Based Assessment for Sampling Personal Characteristics
            </p>
            <div className="flex justify-center gap-4">
              <Button 
                size="lg"
                className="bg-red-700 hover:bg-red-800 text-white"
              >
                Start Free Trial
              </Button>
              <Button 
                size="lg"
                variant="outline"
                className="border-red-700 text-red-700 hover:bg-red-50"
              >
                View Packages
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <motion.div 
            className="max-w-4xl mx-auto"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.2 }}
          >
            <Accordion type="single" collapsible className="w-full space-y-4">
              {features.map((feature, index) => (
                <AccordionItem 
                  key={index} 
                  value={`item-${index}`}
                  className="bg-white rounded-lg shadow-sm border border-gray-200"
                >
                  <AccordionTrigger className="px-6 py-4 hover:bg-gray-50">
                    <h3 className="text-xl font-semibold">{feature.title}</h3>
                  </AccordionTrigger>
                  <AccordionContent className="px-6 pb-4">
                    <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
                      {feature.content.map((item, itemIndex) => (
                        <motion.div
                          key={itemIndex}
                          initial={{ opacity: 0, y: 10 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: itemIndex * 0.1 }}
                          className="bg-gray-50 p-4 rounded-lg"
                        >
                          <h4 className="font-semibold mb-2">{item.title}</h4>
                          <p className="text-gray-600 text-sm">{item.description}</p>
                          {'price' in item && (
                            <p className="text-red-700 font-semibold mt-2">{item.price}</p>
                          )}
                        </motion.div>
                      ))}
                    </div>
                  </AccordionContent>
                </AccordionItem>
              ))}
            </Accordion>
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-red-700 text-white">
        <div className="container mx-auto px-4">
          <motion.div 
            className="max-w-3xl mx-auto text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            <h2 className="text-3xl font-bold mb-6">Ready to Excel in Your CASPer® Test?</h2>
            <p className="text-lg mb-8 text-red-100">
              Join thousands of successful candidates who have prepared with us.
            </p>
            <Button 
              size="lg" 
              variant="outline"
              className="bg-white text-red-700 hover:bg-red-50"
            >
              Get Started Today
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default CasperPrep;
