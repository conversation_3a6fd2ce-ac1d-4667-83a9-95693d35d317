import React, {useState, useEffect} from 'react';
import {ServicesOverview} from '../components/ServicesOverview';
import {ServicePricing} from '../components/ServicePricing';
import ExamPrepAccordion from '../components/ExamPrepAccordion';
import CoachingAccordion from '../components/CoachingAccordion';
import {Calendar} from '../components/ui/calendar';
import {Button} from '../components/ui/button';
import {Card, CardContent, CardHeader, CardTitle} from '../components/ui/card';
import {format} from 'date-fns';
import {Loader2} from 'lucide-react';

const Services: React.FC = () => {
	const [selectedDate, setSelectedDate] = useState<Date | undefined>(new Date());
	const [isLoading, setIsLoading] = useState(false);
	const [isPaymentProcessing, setIsPaymentProcessing] = useState(false);

	// Mock exam data (replace with API call)
	const [mockExams, setMockExams] = useState([
		{
			id: 1,
			name: 'MCCQE Mock Exam',
			description: 'Comprehensive exam to prepare for MCCQE.',
			price: 199,
			available: true,
		},
		{
			id: 2,
			name: 'NAC OSCE Mock Exam',
			description: 'Simulated exam for NAC OSCE preparation.',
			price: 249,
			available: true,
		},
		{
			id: 3,
			name: 'MMI Mock Exam',
			description: 'Practice exam for Multiple Mini Interviews.',
			price: 149,
			available: false,
		},
	]);

	useEffect(() => {
		setIsLoading(true);
		setTimeout(() => {
			setIsLoading(false);
		}, 1000);
	}, []);

	const handlePayment = async () => {
		setIsPaymentProcessing(true);
		// Simulate payment processing delay
		await new Promise((resolve) => setTimeout(resolve, 2000));
		setIsPaymentProcessing(false);
		alert('Payment successful! Check your email for confirmation.');
	};

	return (
		<div className='min-h-screen bg-gradient-to-b from-white to-gray-50 p-4'>
			<ServicesOverview />
			<CoachingAccordion />
			<ServicePricing />
			<ExamPrepAccordion />
			<section className='mt-8'>
				<h2 className='text-3xl font-bold mb-6 text-center'>Mock Exams</h2>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6'>
					{isLoading
						? // Loading state (replace with actual skeleton loading)
							[...Array(3)].map((_, index) => (
								<Card key={index} className='animate-pulse'>
									<CardHeader>
										<div className='h-6 bg-gray-200 rounded w-3/4' />
									</CardHeader>
									<CardContent>
										<div className='h-4 bg-gray-200 rounded w-full mb-2' />
										<div className='h-4 bg-gray-200 rounded w-1/2' />
									</CardContent>
								</Card>
							))
						: mockExams.map((exam) => (
								<Card key={exam.id} className={`transition-transform hover:scale-105 ${!exam.available ? 'opacity-50 cursor-not-allowed' : ''}`}>
									<CardHeader>
										<CardTitle>{exam.name}</CardTitle>
									</CardHeader>
									<CardContent>
										<p>{exam.description}</p>
										<p className='mt-2 font-semibold'>${exam.price}</p>
									</CardContent>
								</Card>
							))}
				</div>
			</section>

			<section className='mt-8'>
				<h2 className='text-3xl font-bold mb-6 text-center'>Select Date and Time</h2>
				<div className='flex justify-center'>
					<Calendar mode='single' selected={selectedDate} onSelect={setSelectedDate} className='rounded-md border' />
				</div>
				{selectedDate && (
					<div className='text-center mt-4'>
						<p>Selected Date: {format(selectedDate, 'MMMM dd, yyyy')}</p>
					</div>
				)}
			</section>

			<section className='mt-8 flex justify-center'>
				<Button
					onClick={handlePayment}
					disabled={isPaymentProcessing || !selectedDate}
					className='transition-colors duration-300 ease-in-out disabled:bg-gray-400'>
					{isPaymentProcessing ? (
						<div className='flex items-center gap-2'>
							<Loader2 className='mr-2 h-4 w-4 animate-spin' />
							Processing...
						</div>
					) : (
						'Proceed to Payment'
					)}
				</Button>
			</section>
		</div>
	);
};

export default Services;
