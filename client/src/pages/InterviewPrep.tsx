import React from 'react';
import { motion } from 'framer-motion';
import { Button } from '../components/ui/button';
import { Star, CheckCircle, Play, Users, Calendar, Award } from 'lucide-react';
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from '../components/ui/card';

const InterviewPrep = () => {
  const features = [
    {
      icon: Users,
      title: 'Expert-Led Sessions',
      description: 'Learn from experienced physicians and interview panelists.'
    },
    {
      icon: Play,
      title: 'Video Recordings',
      description: 'Review and analyze your performance with detailed feedback.'
    },
    {
      icon: Calendar,
      title: 'Flexible Scheduling',
      description: 'Choose from multiple session times that work for you.'
    },
    {
      icon: Award,
      title: 'Success Guarantee',
      description: 'Our proven methodology has helped thousands succeed.'
    }
  ];

  const modules = [
    {
      title: 'Module 1: Interview Fundamentals',
      description: 'Master the basics of medical residency interviews.',
      topics: [
        'Understanding interview formats',
        'Common question types',
        'Professional presentation',
        'Body language and non-verbal communication'
      ]
    },
    {
      title: 'Module 2: MMI Mastery',
      description: 'Specialized training for Multiple Mini Interviews.',
      topics: [
        'Station types and scenarios',
        'Time management strategies',
        'Ethical decision making',
        'Role-play techniques'
      ]
    },
    {
      title: 'Module 3: Clinical Scenarios',
      description: 'Practice handling clinical case discussions.',
      topics: [
        'Patient management scenarios',
        'Emergency situation handling',
        'Team collaboration cases',
        'Medical ethics discussions'
      ]
    },
    {
      title: 'Module 4: Personal Questions',
      description: 'Prepare compelling responses to personal questions.',
      topics: [
        'Background and motivation',
        'Strengths and weaknesses',
        'Challenging situations',
        'Future goals and aspirations'
      ]
    }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-b from-white to-gray-50">
      {/* Hero Section */}
      <section className="relative py-20 bg-gradient-to-r from-yellow-50 to-red-50">
        <div className="container mx-auto px-4">
          <motion.div 
            className="max-w-3xl mx-auto text-center"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex justify-center items-center gap-2 mb-6">
              <Star className="w-8 h-8 text-yellow-400" />
              <h1 className="text-4xl md:text-5xl font-bold">
                Interview Prep Course
              </h1>
              <Star className="w-8 h-8 text-yellow-400" />
            </div>
            <p className="text-xl text-gray-600 mb-8">
              Comprehensive interview preparation designed specifically for medical residency candidates
            </p>
            <div className="flex justify-center gap-4">
              <Button 
                size="lg"
                className="bg-red-700 hover:bg-red-800 text-white"
              >
                Enroll Now
              </Button>
              <Button 
                size="lg"
                variant="outline"
                className="border-red-700 text-red-700 hover:bg-red-50"
              >
                Learn More
              </Button>
            </div>
          </motion.div>
        </div>
      </section>

      {/* Features Grid */}
      <section className="py-20">
        <div className="container mx-auto px-4">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {features.map((feature, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <feature.icon className="w-8 h-8 text-red-700 mb-2" />
                    <CardTitle>{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <CardDescription>{feature.description}</CardDescription>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* Course Modules */}
      <section className="py-20 bg-gray-50">
        <div className="container mx-auto px-4">
          <motion.h2 
            className="text-3xl font-bold text-center mb-12"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
          >
            Course Curriculum
          </motion.h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-6xl mx-auto">
            {modules.map((module, index) => (
              <motion.div
                key={index}
                initial={{ opacity: 0, x: index % 2 === 0 ? -20 : 20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ delay: index * 0.2 }}
              >
                <Card className="h-full hover:shadow-lg transition-shadow">
                  <CardHeader>
                    <CardTitle>{module.title}</CardTitle>
                    <CardDescription>{module.description}</CardDescription>
                  </CardHeader>
                  <CardContent>
                    <ul className="space-y-2">
                      {module.topics.map((topic, topicIndex) => (
                        <li key={topicIndex} className="flex items-start gap-2">
                          <CheckCircle className="w-5 h-5 text-green-600 mt-0.5" />
                          <span>{topic}</span>
                        </li>
                      ))}
                    </ul>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 bg-red-700 text-white">
        <div className="container mx-auto px-4">
          <motion.div 
            className="max-w-3xl mx-auto text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.4 }}
          >
            <h2 className="text-3xl font-bold mb-6">Ready to Ace Your Interviews?</h2>
            <p className="text-lg mb-8 text-red-100">
              Join our comprehensive interview preparation course and gain the confidence you need to succeed.
            </p>
            <Button 
              size="lg" 
              variant="outline"
              className="bg-white text-red-700 hover:bg-red-50"
            >
              Start Your Journey Today
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  );
};

export default InterviewPrep;
