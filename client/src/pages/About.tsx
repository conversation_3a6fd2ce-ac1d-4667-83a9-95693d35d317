import {motion} from 'framer-motion';
import {Users, Award, Heart, Globe} from 'lucide-react';

const values = [
	{
		icon: Users,
		title: 'Community',
		description: 'Building a supportive network of IMGs who help each other succeed.',
	},
	{
		icon: Award,
		title: 'Excellence',
		description: 'Delivering high-quality education and preparation resources.',
	},
	{
		icon: Heart,
		title: 'Compassion',
		description: 'Understanding and supporting IMGs through their unique challenges.',
	},
	{
		icon: Globe,
		title: 'Innovation',
		description: 'Continuously improving our methods and technology to serve IMGs better.',
	},
];

const team = [
	{
		name: 'Dr. <PERSON>',
		role: 'Medical Director',
		image: '/testimonials_image.jpeg',
		description: 'Former IMG with 10+ years of experience in medical education and residency preparation.',
	},
	{
		name: 'Dr. <PERSON>',
		role: 'MCCQE & NAC-OSCE Expert',
		image: '/blog_image.jpeg',
		description: 'Specialized in situational judgment testing and interview preparation for medical professionals.',
	},
];

const About = () => {
	return (
		<div className='bg-white'>
			{/* Hero Section */}
			<section className='relative py-20 bg-gradient-to-b from-red-50 to-white'>
				<div className='container mx-auto px-4'>
					<div className='max-w-3xl mx-auto text-center'>
						<motion.h1 initial={{opacity: 0, y: 20}} animate={{opacity: 1, y: 0}} className='text-4xl md:text-5xl font-bold mb-6'>
							Our Mission to Support IMGs
						</motion.h1>
						<motion.p initial={{opacity: 0, y: 20}} animate={{opacity: 1, y: 0}} transition={{delay: 0.2}} className='text-xl text-gray-600'>
							Dedicated to empowering international medical graduates in their journey to becoming licensed physicians in Canada and the United
							States.
						</motion.p>
					</div>
				</div>
			</section>

			{/* Story Section */}
			<section className='py-20'>
				<div className='container mx-auto px-4'>
					<div className='max-w-3xl mx-auto'>
						<motion.div initial={{opacity: 0, y: 20}} animate={{opacity: 1, y: 0}} className='prose prose-lg mx-auto'>
							<h2 className='text-3xl font-bold mb-6 text-center'>Our Story</h2>
							<p className='text-gray-600 mb-6'>
								Founded by a team of successful IMGs who understand firsthand the challenges of navigating medical licensing and residency matching in
								North America, Dr IMG Abroad was created to provide the support and resources we wished we had during our own journeys.
							</p>
							<p className='text-gray-600 mb-6'>
								Our platform has grown from a small mentorship program to a comprehensive resource hub, helping thousands of IMGs achieve their dreams
								of practicing medicine in Canada and the USA.
							</p>
						</motion.div>
					</div>
				</div>
			</section>

			{/* Values Section */}
			<section className='py-20 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold mb-12 text-center'>Our Values</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8'>
						{values.map((value, index) => (
							<motion.div
								key={value.title}
								initial={{opacity: 0, y: 20}}
								animate={{opacity: 1, y: 0}}
								transition={{delay: index * 0.1}}
								className='text-center'>
								<div className='inline-block p-4 bg-white rounded-full shadow-lg mb-6'>
									<value.icon className='w-8 h-8 text-red-600' />
								</div>
								<h3 className='text-xl font-semibold mb-4'>{value.title}</h3>
								<p className='text-gray-600'>{value.description}</p>
							</motion.div>
						))}
					</div>
				</div>
			</section>

			{/* Team Section */}
			<section className='py-20'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold mb-12 text-center'>Meet Our Expert Team</h2>
					<div className='grid grid-cols-1 md:grid-cols-2 gap-12 max-w-5xl mx-auto'>
						{team.map((member, index) => (
							<motion.div
								key={member.name}
								initial={{opacity: 0, y: 20}}
								animate={{opacity: 1, y: 0}}
								transition={{delay: index * 0.1}}
								className='bg-white rounded-xl shadow-lg overflow-hidden'>
								<img src={member.image} alt={member.name} className='w-full h-64 object-cover' />
								<div className='p-6'>
									<h3 className='text-xl font-semibold mb-2'>{member.name}</h3>
									<p className='text-red-600 font-medium mb-4'>{member.role}</p>
									<p className='text-gray-600'>{member.description}</p>
								</div>
							</motion.div>
						))}
					</div>
				</div>
			</section>

			{/* Impact Section */}
			<section className='py-20 bg-gray-50'>
				<div className='container mx-auto px-4'>
					<h2 className='text-3xl font-bold mb-12 text-center'>Our Impact</h2>
					<div className='grid grid-cols-1 md:grid-cols-3 gap-8 max-w-4xl mx-auto text-center'>
						<motion.div initial={{opacity: 0, y: 20}} animate={{opacity: 1, y: 0}} className='bg-white p-8 rounded-xl shadow-lg'>
							<h3 className='text-4xl font-bold text-red-600 mb-2'>90%+</h3>
							<p className='text-gray-600'>Success rate in MCCQE & NAC-OSCE exam preparation</p>
						</motion.div>
						<motion.div
							initial={{opacity: 0, y: 20}}
							animate={{opacity: 1, y: 0}}
							transition={{delay: 0.1}}
							className='bg-white p-8 rounded-xl shadow-lg'>
							<h3 className='text-4xl font-bold text-red-600 mb-2'>1000+</h3>
							<p className='text-gray-600'>IMGs successfully matched into residency</p>
						</motion.div>
						<motion.div
							initial={{opacity: 0, y: 20}}
							animate={{opacity: 1, y: 0}}
							transition={{delay: 0.2}}
							className='bg-white p-8 rounded-xl shadow-lg'>
							<h3 className='text-4xl font-bold text-red-600 mb-2'>50+</h3>
							<p className='text-gray-600'>Countries represented in our community</p>
						</motion.div>
					</div>
				</div>
			</section>
		</div>
	);
};

export default About;
