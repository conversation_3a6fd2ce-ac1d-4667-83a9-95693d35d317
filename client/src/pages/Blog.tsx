import React, { useState, useEffect, useRef } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { BlogList } from '../components/BlogList';
import { CategoryFilter } from '../components/CategoryFilter';
import { SubscribeForm } from '../components/SubscribeForm';
import { BlogPost } from '../components/BlogPost';
import { fakeBlogPosts } from '../lib/fakeData';
import { Skeleton } from '../components/ui/skeleton';
import { Input } from '../components/ui/input';

interface BlogPost {
  id: number;
  title: string;
  content: string;
  author: string;
  date: string;
  category: string;
}

const Blog: React.FC = () => {
  const searchRef = useRef<HTMLInputElement>(null);
  
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    },
    exit: { opacity: 0, y: 20 }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 }
    }
  };
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [selectedPost, setSelectedPost] = useState<BlogPost | null>(null);
  const [searchTerm, setSearchTerm] = useState('');
  const [loading, setLoading] = useState(true);
  const [blogPosts, setBlogPosts] = useState<BlogPost[]>([]);

  useEffect(() => {
    const timer = setTimeout(() => {
      setBlogPosts(fakeBlogPosts);
      setLoading(false);
    }, 1500);

    return () => clearTimeout(timer);
  }, []);

  const handlePostClick = (post: BlogPost) => {
    setSelectedPost(post);
  };

  const handleBackToList = () => {
    setSelectedPost(null);
  };

  const handleSearchChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(event.target.value);
  };

  const filteredPosts = blogPosts.filter((post) =>
    post.title.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <motion.div 
      className="container mx-auto px-4 py-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
      exit="exit"
    >
      <motion.div
        variants={itemVariants}
        className="text-center mb-16"
      >
        <motion.span 
          className="inline-block px-4 py-1 mb-4 text-sm font-medium rounded-full bg-red-100 text-red-800"
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          Latest Updates
        </motion.span>
        <motion.h1 
          className="text-4xl md:text-5xl font-bold mb-4 relative inline-block"
        >
          Blog & Resources
          <motion.span
            className="absolute -bottom-2 left-0 w-full h-1 bg-red-200"
            initial={{ scaleX: 0 }}
            animate={{ scaleX: 1 }}
            transition={{ delay: 0.5, duration: 0.6 }}
          />
        </motion.h1>
        <motion.p 
          className="text-gray-600 max-w-2xl mx-auto mb-8"
          variants={itemVariants}
        >
          Stay updated with the latest insights, tips, and success stories from our IMG community
        </motion.p>
      </motion.div>

      <motion.div 
        className="mb-4 flex justify-center"
        variants={itemVariants}
      >
        <motion.div 
          className="relative w-full max-w-md"
          whileHover={{ scale: 1.02 }}
          transition={{ duration: 0.2 }}
        >
          <Input
            ref={searchRef}
            type="text"
            placeholder="Search blog posts..."
            value={searchTerm}
            onChange={handleSearchChange}
            className="w-full pr-10 focus:ring-2 focus:ring-red-500 transition-shadow duration-200"
          />
          <motion.span 
            className="absolute right-3 top-1/2 transform -translate-y-1/2 text-gray-400"
            animate={{ scale: searchTerm ? 1.1 : 1 }}
          >
            🔍
          </motion.span>
        </motion.div>
      </motion.div>
      <AnimatePresence mode="wait">
        {selectedPost ? (
          <motion.div 
            className="p-4"
            key="post"
            initial={{ opacity: 0, x: 20 }}
            animate={{ opacity: 1, x: 0 }}
            exit={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.3 }}
          >
            <BlogPost post={selectedPost} onBack={handleBackToList} />
          </motion.div>
        ) : (
          <motion.div 
            className="flex flex-col md:flex-row gap-8"
            key="list"
            variants={containerVariants}
            initial="hidden"
            animate="visible"
            exit="exit"
          >
            <motion.div 
              className="md:w-3/4"
              variants={itemVariants}
            >
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.2 }}
              >
                <CategoryFilter onSelectCategory={setSelectedCategory} />
              </motion.div>
              
              {loading ? (
                <motion.div 
                  className="space-y-4"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.3 }}
                >
                  {[...Array(4)].map((_, i) => (
                    <motion.div
                      key={i}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ delay: i * 0.1 }}
                    >
                      <Skeleton className="h-6 w-full mb-2" />
                      <Skeleton className="h-40 w-full" />
                    </motion.div>
                  ))}
                </motion.div>
              ) : (
                <BlogList
                  posts={filteredPosts}
                  category={selectedCategory}
                  onPostClick={handlePostClick}
                />
              )}
            </motion.div>
            <motion.div 
              className="md:w-1/4"
              variants={itemVariants}
            >
              <SubscribeForm />
            </motion.div>
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default Blog;
