import {useState} from 'react';
import {motion, AnimatePresence} from 'framer-motion';
import {But<PERSON>} from './ui/button';
import {Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle} from './ui/card';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from './ui/select';
import {Calendar} from './ui/calendar';
import {Badge} from './ui/badge';
import {Clock, Users, CheckCircle, BookOpen, Loader2, AlertCircle} from 'lucide-react';
import {Alert, AlertDescription} from './ui/alert';

const mockExams = [
	{
		id: 'MCCQE',
		name: 'MCCQE Mock Exam',
		description: 'Complete simulation of the Medical Council of Canada Qualifying Examination Part I',
		duration: '4 hours',
		price: 299,
		features: [
			'Full-length exam simulation',
			'Clinical decision-making scenarios',
			'Detailed performance analysis',
			'Personalized feedback report',
			'Review session with expert',
		],
	},
	{
		id: 'nac-osce',
		name: 'NAC OSCE Practice',
		description: 'Structured clinical examination practice with standardized patients',
		duration: '3 hours',
		price: 349,
		features: [
			'12 station circuit',
			'Standardized patient interactions',
			'Clinical skills assessment',
			'Communication feedback',
			'Expert evaluator scoring',
		],
	},
	{
		id: 'mmi',
		name: 'MMI Interview Practice',
		description: 'Multiple Mini Interview preparation with various scenarios',
		duration: '2 hours',
		price: 249,
		features: [
			'8 interview stations',
			'Ethical scenario practice',
			'Professional behavior assessment',
			'Immediate feedback',
			'Performance recording',
		],
	},
	{
		id: 'MCCQE-nac-osce',
		name: 'MCCQE & NAC-OSCE Exam Simulation',
		description: 'Computer-based situational judgment test practice',
		duration: '90 minutes',
		price: 199,
		features: [
			'12 scenario-based sections',
			'Timed responses',
			'Behavioral assessment',
			'Written communication evaluation',
			'Detailed feedback report',
		],
	},
];

const timeSlots = ['9:00 AM', '10:00 AM', '11:00 AM', '1:00 PM', '2:00 PM', '3:00 PM'];

export const MockExamBooking = () => {
	const [selectedExam, setSelectedExam] = useState<string>('');
	const [selectedDate, setSelectedDate] = useState<Date>();
	const [selectedTime, setSelectedTime] = useState<string>('');
	const [isBooking, setIsBooking] = useState(false);
	const [showSuccess, setShowSuccess] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleBooking = async () => {
		try {
			setIsBooking(true);
			setError(null);
			// Simulate API call
			await new Promise((resolve) => setTimeout(resolve, 2000));
			setShowSuccess(true);
			setTimeout(() => setShowSuccess(false), 3000);
		} catch (err) {
			setError('Failed to book the exam. Please try again.');
		} finally {
			setIsBooking(false);
		}
	};

	const containerVariants = {
		hidden: {opacity: 0, y: 20},
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.6,
				staggerChildren: 0.1,
			},
		},
	};

	const cardVariants = {
		hidden: {opacity: 0, y: 20},
		visible: {
			opacity: 1,
			y: 0,
			transition: {duration: 0.4},
		},
	};

	return (
		<section className='py-20 bg-gradient-to-b from-white to-gray-50'>
			<div className='container mx-auto px-4'>
				<motion.div className='text-center mb-16' initial={{opacity: 0, y: 20}} animate={{opacity: 1, y: 0}} transition={{duration: 0.6}}>
					<motion.span
						className='inline-block px-4 py-1 mb-4 text-sm font-medium rounded-full bg-red-100 text-red-800'
						whileHover={{scale: 1.05}}
						transition={{duration: 0.2}}>
						Mock Exams
					</motion.span>
					<motion.h2
						className='text-3xl md:text-4xl font-bold mb-4'
						variants={{
							hidden: {opacity: 0, y: 20},
							visible: {opacity: 1, y: 0},
						}}
						initial='hidden'
						animate='visible'
						transition={{duration: 0.6, delay: 0.2}}>
						Practice Makes Perfect
					</motion.h2>
					<p className='text-gray-600 max-w-2xl mx-auto'>
						Experience realistic exam conditions with our comprehensive mock exam sessions. Get detailed feedback and improve your performance.
					</p>
				</motion.div>

				<motion.div
					className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-2 gap-8 mb-16'
					variants={containerVariants}
					initial='hidden'
					animate='visible'>
					{mockExams.map((exam) => (
						<motion.div key={exam.id} variants={cardVariants} whileHover={{scale: 1.02}} transition={{duration: 0.2}}>
							<Card className='h-full'>
								<CardHeader>
									<CardTitle>{exam.name}</CardTitle>
									<CardDescription>{exam.description}</CardDescription>
									<div className='flex items-center gap-4 mt-2'>
										<Badge variant='secondary' className='flex items-center gap-1'>
											<Clock className='w-4 h-4' />
											{exam.duration}
										</Badge>
										<Badge variant='secondary' className='flex items-center gap-1'>
											<Users className='w-4 h-4' />
											Limited Spots
										</Badge>
									</div>
								</CardHeader>
								<CardContent>
									<ul className='space-y-2'>
										{exam.features.map((feature, index) => (
											<motion.li
												key={index}
												className='flex items-start gap-2'
												initial={{opacity: 0, x: -20}}
												animate={{opacity: 1, x: 0}}
												transition={{delay: index * 0.1}}>
												<CheckCircle className='w-5 h-5 text-green-500 flex-shrink-0 mt-0.5' />
												<span className='text-gray-600'>{feature}</span>
											</motion.li>
										))}
									</ul>
								</CardContent>
								<CardFooter className='flex flex-col items-stretch gap-4'>
									<div className='text-2xl font-bold text-center w-full'>${exam.price}</div>
									<Button
										className='w-full bg-red-700 hover:bg-red-800 text-white relative overflow-hidden group'
										onClick={() => setSelectedExam(exam.id)}>
										<motion.span
											className='absolute inset-0 bg-white opacity-0 group-hover:opacity-10'
											initial={false}
											animate={{scale: [1, 2], opacity: [0, 0.1, 0]}}
											transition={{duration: 0.5, repeat: Infinity}}
										/>
										Book Now
									</Button>
								</CardFooter>
							</Card>
						</motion.div>
					))}
				</motion.div>

				<AnimatePresence mode='wait'>
					{selectedExam && (
						<motion.div
							initial={{opacity: 0, y: 20}}
							animate={{opacity: 1, y: 0}}
							exit={{opacity: 0, y: -20}}
							className='max-w-3xl mx-auto bg-white rounded-2xl p-8 shadow-lg border border-gray-100'>
							<h3 className='text-2xl font-bold mb-6'>Schedule Your Mock Exam</h3>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-8'>
								<div>
									<h4 className='font-medium mb-4'>Select Date</h4>
									<Calendar
										mode='single'
										selected={selectedDate}
										onSelect={setSelectedDate}
										className='rounded-md border'
										disabled={(date) => date < new Date()}
									/>
								</div>
								<div>
									<h4 className='font-medium mb-4'>Select Time Slot</h4>
									<Select onValueChange={setSelectedTime}>
										<SelectTrigger>
											<SelectValue placeholder='Choose a time' />
										</SelectTrigger>
										<SelectContent>
											{timeSlots.map((time) => (
												<SelectItem key={time} value={time}>
													{time}
												</SelectItem>
											))}
										</SelectContent>
									</Select>

									{selectedDate && selectedTime && (
										<motion.div initial={{opacity: 0, height: 0}} animate={{opacity: 1, height: 'auto'}} className='mt-8'>
											<h4 className='font-medium mb-4'>Booking Summary</h4>
											<div className='space-y-2 text-gray-600'>
												<p>
													<strong>Exam:</strong> {mockExams.find((e) => e.id === selectedExam)?.name}
												</p>
												<p>
													<strong>Date:</strong> {selectedDate.toLocaleDateString()}
												</p>
												<p>
													<strong>Time:</strong> {selectedTime}
												</p>
												<p>
													<strong>Price:</strong> ${mockExams.find((e) => e.id === selectedExam)?.price}
												</p>
											</div>

											<AnimatePresence mode='wait'>
												{error && (
													<motion.div
														initial={{opacity: 0, height: 0}}
														animate={{opacity: 1, height: 'auto'}}
														exit={{opacity: 0, height: 0}}
														className='mt-4'>
														<Alert variant='destructive'>
															<AlertCircle className='h-4 w-4' />
															<AlertDescription>{error}</AlertDescription>
														</Alert>
													</motion.div>
												)}
											</AnimatePresence>

											<Button
												className='w-full mt-6 bg-red-700 hover:bg-red-800 text-white relative overflow-hidden group'
												onClick={handleBooking}
												disabled={isBooking}>
												<motion.span
													className='absolute inset-0 bg-white opacity-0 group-hover:opacity-10'
													initial={false}
													animate={{scale: [1, 2], opacity: [0, 0.1, 0]}}
													transition={{duration: 0.5, repeat: Infinity}}
												/>
												{isBooking ? (
													<>
														<Loader2 className='mr-2 h-4 w-4 animate-spin' />
														Processing...
													</>
												) : showSuccess ? (
													<>
														<CheckCircle className='mr-2 h-4 w-4' />
														Booking Confirmed!
													</>
												) : (
													'Confirm Booking'
												)}
											</Button>
										</motion.div>
									)}
								</div>
							</div>
						</motion.div>
					)}
				</AnimatePresence>

				<motion.div className='mt-12 text-center text-gray-600' initial={{opacity: 0}} animate={{opacity: 1}} transition={{delay: 0.8}}>
					<p className='mb-4'>Need to reschedule? Contact us at least 48 hours before your exam.</p>
					<p>All mock exams include detailed feedback and performance analysis.</p>
				</motion.div>
			</div>
		</section>
	);
};
