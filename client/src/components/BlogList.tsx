import React from 'react';
import { motion } from 'framer-motion';
import { Calendar, User, Tag, ArrowRight } from 'lucide-react';

interface BlogPost {
  id: number;
  title: string;
  content: string;
  author: string;
  date: string;
  category: string;
}

interface BlogListProps {
  posts: BlogPost[];
  category: string | null;
  onPostClick: (post: BlogPost) => void;
}

export const BlogList: React.FC<BlogListProps> = ({ posts, category, onPostClick }) => {
  const filteredPosts = category
    ? posts.filter(post => post.category === category)
    : posts;

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.3 }
    }
  };

  return (
    <motion.div 
      className="space-y-6"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {filteredPosts.map((post, index) => (
        <motion.article
          key={post.id}
          variants={itemVariants}
          whileHover={{ y: -5 }}
          onClick={() => onPostClick(post)}
          className="bg-white rounded-lg shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer overflow-hidden group"
        >
          <div className="p-6">
            <div className="flex items-center gap-3 mb-4">
              <motion.span 
                className="inline-flex items-center bg-red-100 text-red-800 rounded-full px-3 py-1 text-sm font-medium"
                whileHover={{ scale: 1.05 }}
              >
                <Tag className="w-4 h-4 mr-1" />
                {post.category}
              </motion.span>
              <span className="flex items-center text-gray-600 text-sm">
                <Calendar className="w-4 h-4 mr-1" />
                {post.date}
              </span>
            </div>

            <motion.h3 
              className="text-xl font-bold mb-2 text-gray-900 group-hover:text-red-700 transition-colors duration-200"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: index * 0.1 }}
            >
              {post.title}
            </motion.h3>

            <p className="text-gray-600 mb-4 line-clamp-2">
              {post.content}
            </p>

            <div className="flex items-center justify-between">
              <span className="flex items-center text-sm text-gray-600">
                <User className="w-4 h-4 mr-1" />
                {post.author}
              </span>

              <motion.div
                className="flex items-center text-red-700 text-sm font-medium opacity-0 group-hover:opacity-100 transition-opacity duration-200"
                initial={false}
                animate={{ x: 0 }}
                whileHover={{ x: 5 }}
              >
                Read More
                <ArrowRight className="w-4 h-4 ml-1" />
              </motion.div>
            </div>
          </div>
        </motion.article>
      ))}
    </motion.div>
  );
};
