import React from 'react';
import {useTheme} from '../contexts/ThemeContext';
import {Button} from './ui/button';
import {Moon, Sun} from 'lucide-react';

const ThemeToggle: React.FC = () => {
	const {theme, toggleTheme} = useTheme();

	return (
		<Button variant='outline' size='icon' onClick={toggleTheme} aria-label={theme === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}>
			{theme === 'light' ? <Moon className='h-[1.2rem] w-[1.2rem]' /> : <Sun className='h-[1.2rem] w-[1.2rem]' />}
		</Button>
	);
};

export default ThemeToggle;
