import {motion, useScroll, useTransform} from 'framer-motion';
import {Button} from '../components/ui/button';
import {useRef} from 'react';

export const Hero = () => {
	const ref = useRef<HTMLElement>(null);
	const {scrollYProgress} = useScroll({
		target: ref,
		offset: ['start start', 'end start'],
	});

	const opacity = useTransform(scrollYProgress, [0, 1], [1, 0]);
	const scale = useTransform(scrollYProgress, [0, 1], [1, 0.95]);
	const containerVariants = {
		hidden: {opacity: 0},
		visible: {
			opacity: 1,
			transition: {
				staggerChildren: 0.2,
				delayChildren: 0.3,
			},
		},
	};

	const backgroundVariants = {
		hidden: {opacity: 0, scale: 1.1},
		visible: {
			opacity: 0.15,
			scale: 1,
			transition: {duration: 1, ease: 'easeOut'},
		},
	};

	const itemVariants = {
		hidden: {opacity: 0, y: 20},
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.6,
				ease: 'easeOut',
			},
		},
	};

	const buttonHoverVariants = {
		hover: {
			scale: 1.05,
			transition: {
				duration: 0.2,
				ease: 'easeInOut',
			},
		},
		tap: {
			scale: 0.95,
		},
	};

	return (
		<motion.section
			ref={ref}
			initial='hidden'
			animate='visible'
			variants={containerVariants}
			style={{opacity, scale}}
			className='relative min-h-[90vh] flex items-center justify-center overflow-hidden'
			aria-label='Welcome section'>
			<motion.div variants={backgroundVariants} className='absolute inset-0 bg-gradient-to-r from-gray-50 to-red-50' />
			<motion.div
				className='absolute inset-0'
				initial={{opacity: 0}}
				animate={{opacity: 0.1}}
				transition={{duration: 1}}
				style={{
					backgroundSize: 'cover',
					backgroundPosition: 'center',
					filter: 'blur(8px)',
				}}
			/>
			<div className='container mx-auto px-4 flex flex-col md:flex-row items-center'>
				<motion.div variants={itemVariants} className='md:w-1/2 text-left md:pr-8'>
					<motion.span
						variants={itemVariants}
						whileHover={{scale: 1.05}}
						className='inline-block px-4 py-1 mb-6 text-sm font-medium rounded-full bg-red-100 text-red-800 backdrop-blur-sm'
						role='text'
						aria-label='Welcome banner'>
						Your MCCQE and NAC Success Starts Here
					</motion.span>
					<motion.h1
						variants={itemVariants}
						className='text-4xl md:text-6xl font-bold mb-4 leading-tight text-gray-900 drop-shadow-sm'
						role='heading'
						aria-level={1}>
						Your Time To
						<br />
						<motion.span
							variants={itemVariants}
							className='text-red-700'
							whileHover={{
								scale: 1.02,
								color: '#991B1B',
								transition: {duration: 0.2},
							}}>
							Ace The MCCQE & NAC-OSCE Exams
						</motion.span>{' '}
						Test
					</motion.h1>
					<motion.p variants={itemVariants} className='text-lg md:text-xl text-gray-700 mb-8'>
						Expert-led preparation and personalized coaching to help you excel in your MCCQE & NAC-OSCE exams. Join thousands of successful medical
						professionals who trusted our proven methodology.
					</motion.p>
					<motion.div variants={itemVariants} className='flex flex-col sm:flex-row gap-4'>
						<motion.div variants={buttonHoverVariants} whileHover='hover' whileTap='tap'>
							<Button
								size='lg'
								className='bg-red-700 hover:bg-red-800 text-white text-lg flex items-center group relative overflow-hidden'
								aria-label='Start your preparation'>
								<motion.span
									className='absolute inset-0 bg-white opacity-0 group-hover:opacity-10 transition-opacity duration-300'
									initial={false}
									animate={{scale: [1, 2], opacity: [0, 0.1, 0]}}
									transition={{duration: 0.5, repeat: Infinity}}
								/>
								Start Your Preparation
								<motion.svg
									className='ml-2 w-5 h-5'
									viewBox='0 0 24 24'
									fill='none'
									xmlns='http://www.w3.org/2000/svg'
									animate={{x: [0, 5, 0]}}
									transition={{
										duration: 1.5,
										repeat: Infinity,
										ease: 'easeInOut',
									}}>
									<path d='M5 12H19M19 12L12 5M19 12L12 19' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' />
								</motion.svg>
							</Button>
						</motion.div>
						<motion.div variants={buttonHoverVariants} whileHover='hover' whileTap='tap'>
							<Button
								variant='outline'
								size='lg'
								className='border-red-700 text-red-700 hover:bg-red-50 text-lg relative overflow-hidden'
								aria-label='View success stories'>
								<motion.span
									className='absolute inset-0 bg-red-700 opacity-0 group-hover:opacity-5 transition-opacity duration-300'
									initial={false}
									animate={{scale: [1, 2], opacity: [0, 0.05, 0]}}
									transition={{duration: 0.5, repeat: Infinity}}
								/>
								View Success Stories
							</Button>
						</motion.div>
					</motion.div>
				</motion.div>
				<motion.div
					variants={itemVariants}
					className='md:w-1/2 mt-8 md:mt-0'
					whileHover={{
						scale: 1.02,
						transition: {duration: 0.3},
					}}>
					<motion.div className='relative rounded-lg shadow-xl overflow-hidden'>
						<motion.img
							src='/hero_image.jpeg'
							alt='Doctor consulting with a patient'
							className='w-full h-auto object-cover'
							initial={{filter: 'blur(5px)', scale: 1.1}}
							animate={{filter: 'blur(0px)', scale: 1}}
							transition={{duration: 0.8, ease: 'easeOut'}}
							loading='eager'
							onLoad={(e) => {
								const img = e.currentTarget;
								if (img.complete) {
									img.style.opacity = '1';
								}
							}}
						/>
						<motion.div
							className='absolute inset-0 bg-gradient-to-t from-black/20 to-transparent'
							initial={{opacity: 0}}
							animate={{opacity: 1}}
							transition={{delay: 0.5}}
						/>
					</motion.div>
				</motion.div>
			</div>
		</motion.section>
	);
};
