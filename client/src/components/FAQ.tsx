import {useRef} from 'react';
import {motion, useInView} from 'framer-motion';
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from './ui/accordion';
import {HelpCircle, ArrowRight, MessageCircle} from 'lucide-react';
import {Button} from './ui/button';

const faqs = [
	{
		question: 'What are the MCCQE & NAC-OSCE exams?',
		answer:
			'The MCCQE (Medical Council of Canada Qualifying Examination Part 1) and NAC-OSCE (National Assessment Collaboration Objective Structured Clinical Examination) are critical exams for International Medical Graduates seeking medical licensure in Canada. They assess both medical knowledge and clinical skills.',
	},
	{
		question: 'How long does it take to prepare for the MCCQE & NAC-OSCE exams?',
		answer:
			'Preparation time varies by individual, but we recommend dedicating 4-6 weeks for comprehensive preparation. Our structured program helps you efficiently prepare through practice scenarios, personalized feedback, and expert coaching.',
	},
	{
		question: "What's included in your MCCQE & NAC-OSCE preparation packages?",
		answer:
			'Our packages include full-length mock tests, personalized feedback, practice scenarios, expert coaching sessions, study materials, and access to our online learning platform. The specific features vary by package level - Essential, Complete, or Elite.',
	},
	{
		question: 'Do you offer one-on-one coaching?',
		answer:
			'Yes, we offer personalized one-on-one coaching sessions with experienced medical professionals who understand the MCCQE & NAC-OSCE exam requirements. These sessions can be scheduled at your convenience and are included in our Complete and Elite packages.',
	},
	{
		question: 'How does the study partner matching system work?',
		answer:
			"Our study partner matching system uses an algorithm to connect you with compatible study partners based on factors like study schedule, exam timeline, learning style, and goals. You can specify your preferences and we'll find suitable matches within our community.",
	},
	{
		question: 'What is your success rate?',
		answer:
			'Our students consistently achieve above-average scores on their MCCQE & NAC-OSCE exams. Over 90% of our students who complete our comprehensive preparation program report feeling well-prepared and confident during their actual exams.',
	},
	{
		question: "Can I get a refund if I'm not satisfied?",
		answer:
			"Yes, we offer a 7-day money-back guarantee on all our packages. If you're not satisfied with our services within the first week, you can request a full refund, no questions asked.",
	},
	{
		question: 'Do you provide support for both Canadian and US medical residency applications?',
		answer:
			'Yes, we provide comprehensive support for both CaRMS (Canadian Residency Matching Service) and ERAS (Electronic Residency Application Service) applications. Our experts are familiar with both systems and their specific requirements.',
	},
];

export const FAQ = () => {
	const ref = useRef(null);
	const isInView = useInView(ref, {once: true, margin: '-100px'});

	const containerVariants = {
		hidden: {opacity: 0},
		visible: {
			opacity: 1,
			transition: {
				staggerChildren: 0.1,
				delayChildren: 0.3,
			},
		},
	};

	const itemVariants = {
		hidden: {opacity: 0, y: 20},
		visible: {
			opacity: 1,
			y: 0,
			transition: {duration: 0.5},
		},
	};
	return (
		<section className='py-20 bg-gradient-to-b from-white to-gray-50 overflow-hidden' ref={ref}>
			<motion.div className='container mx-auto px-4' variants={containerVariants} initial='hidden' animate={isInView ? 'visible' : 'hidden'}>
				<motion.div className='text-center mb-16' variants={itemVariants}>
					<motion.div
						className='inline-flex items-center gap-2 px-4 py-1 mb-4 text-sm font-medium rounded-full bg-red-100 text-red-800'
						whileHover={{scale: 1.05}}
						whileTap={{scale: 0.95}}>
						<HelpCircle className='w-4 h-4' />
						FAQ
					</motion.div>
					<motion.h2 className='text-3xl md:text-4xl font-bold mb-4 relative inline-block'>
						Frequently Asked Questions
						<motion.span
							className='absolute -bottom-2 left-0 w-full h-1 bg-red-200'
							initial={{scaleX: 0}}
							animate={isInView ? {scaleX: 1} : {scaleX: 0}}
							transition={{delay: 0.5, duration: 0.6}}
						/>
					</motion.h2>
					<motion.p className='text-gray-600 max-w-2xl mx-auto' variants={itemVariants}>
						Find answers to common questions about our services and the MCCQE & NAC-OSCE preparation process
					</motion.p>
				</motion.div>

				<motion.div className='max-w-3xl mx-auto' variants={itemVariants}>
					<Accordion type='single' collapsible className='w-full space-y-4'>
						{faqs.map((faq, index) => (
							<motion.div key={index} variants={itemVariants} custom={index} initial='hidden' animate='visible' transition={{delay: index * 0.1}}>
								<AccordionItem
									value={`item-${index}`}
									className='border border-gray-200 rounded-lg overflow-hidden bg-white shadow-sm hover:shadow-md transition-shadow duration-200'>
									<AccordionTrigger className='text-left px-6 py-4 hover:bg-gray-50 transition-colors duration-200'>
										<span className='text-gray-900 font-medium'>{faq.question}</span>
									</AccordionTrigger>
									<AccordionContent className='px-6 py-4 text-gray-600 bg-gray-50/50'>
										<motion.div initial={{opacity: 0, y: -10}} animate={{opacity: 1, y: 0}} transition={{duration: 0.2}}>
											{faq.answer}
										</motion.div>
									</AccordionContent>
								</AccordionItem>
							</motion.div>
						))}
					</Accordion>
				</motion.div>

				<motion.div className='text-center mt-16' variants={itemVariants}>
					<motion.div
						className='bg-white rounded-xl p-8 shadow-lg border border-gray-100 max-w-2xl mx-auto'
						whileHover={{y: -5}}
						transition={{duration: 0.2}}>
						<motion.div className='flex items-center justify-center mb-4'>
							<MessageCircle className='w-8 h-8 text-red-700' />
						</motion.div>
						<motion.h3 className='text-xl font-bold mb-2' variants={itemVariants}>
							Still have questions?
						</motion.h3>
						<motion.p className='text-gray-600 mb-6' variants={itemVariants}>
							Our team is here to help you with any questions you might have about our services.
						</motion.p>
						<Button className='bg-red-700 hover:bg-red-800 text-white relative overflow-hidden group'>
							<motion.span
								className='absolute inset-0 bg-white opacity-0 group-hover:opacity-10'
								initial={false}
								animate={{scale: [1, 2], opacity: [0, 0.1, 0]}}
								transition={{duration: 0.5, repeat: Infinity}}
							/>
							<span className='flex items-center'>
								Contact Us
								<ArrowRight className='ml-2 w-4 h-4 transition-transform group-hover:translate-x-1' />
							</span>
						</Button>
					</motion.div>
				</motion.div>
			</motion.div>
		</section>
	);
};
