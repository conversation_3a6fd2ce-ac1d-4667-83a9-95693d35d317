import {motion} from 'framer-motion';
import {Calendar} from './ui/calendar';
import {Button} from './ui/button';
import {Input} from './ui/input';
import {Textarea} from './ui/textarea';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from './ui/select';
import {useState, useEffect} from 'react'; // Added useEffect
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query'; // Added react-query hooks
import { toast } from './ui/use-toast'; // Added toast

// Define TimeSlot type as expected from API
interface TimeSlot {
  startTime: string; // ISO string
  endTime: string;   // ISO string
}

const consultationTypes = [
	{id: 'MCCQE-nac-osce', name: 'MCCQE & NAC-OSCE Exam Preparation'},
	{id: 'residency', name: 'Residency Application Strategy'},
	{id: 'interview', name: 'Interview Preparation'},
	{id: 'career', name: 'Career Planning'},
	{id: 'general', name: 'General Consultation'},
];

// Removed hardcoded timeSlots

export const BookConsultation = () => {
  const [userName, setUserName] = useState('');
  const [userEmail, setUserEmail] = useState('');
  const [notes, setNotes] = useState('');
  const [selectedTimeSlotIso, setSelectedTimeSlotIso] = useState<string | undefined>(undefined);
	const [date, setDate] = useState<Date | undefined>(undefined);
	const [consultationType, setConsultationType] = useState('');
  // Removed old timeSlot state

  const queryClient = useQueryClient();
  const slotDurationMinutes = 60;

  // Fetch Available Slots
  const fetchAvailableSlots = async (selectedDate: Date | undefined): Promise<TimeSlot[]> => {
    if (!selectedDate) return []; // Should be disabled if no date, but defensive check

    const dateStart = selectedDate.toISOString().split('T')[0];
    const dateEnd = dateStart; // For this MVP, we fetch for a single day

    const response = await fetch(
      `/api/available-slots?date_start=${dateStart}&date_end=${dateEnd}&slot_duration_minutes=${slotDurationMinutes}`
    );
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch available slots');
    }
    return response.json();
  };

  const {
    data: availableSlotsData,
    isLoading: isLoadingSlots,
    error: slotsError
  } = useQuery<TimeSlot[], Error>(
    ['availableSlots', date?.toISOString().split('T')[0], slotDurationMinutes],
    () => fetchAvailableSlots(date),
    {
      enabled: !!date, // Only run if a date is selected
      staleTime: 5 * 60 * 1000, // Cache for 5 minutes
      refetchOnWindowFocus: true, // Refetch on focus for up-to-date slots
      onError: (err) => {
        toast({
          title: "Error",
          description: `Could not load time slots: ${err.message}`,
          variant: "destructive"
        })
      }
    }
  );

  // Reset selected time slot if date changes or available slots change
  useEffect(() => {
    setSelectedTimeSlotIso(undefined);
  }, [date, availableSlotsData]);


  // Booking Submission Mutation
  const submitBookingFn = async (bookingDetails: {
    user_id: number; // Assuming a placeholder or future implementation for user_id
    start_time: string;
    end_time: string;
    title?: string;
    notes?: string;
  }) => {
    const response = await fetch('/api/bookings', {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify(bookingDetails),
    });
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Booking request failed');
    }
    return response.json();
  };

  const bookingMutation = useMutation(submitBookingFn, {
    onSuccess: () => {
      toast({ title: 'Success!', description: 'Booking confirmed successfully.' });
      queryClient.invalidateQueries(['availableSlots', date?.toISOString().split('T')[0], slotDurationMinutes]);
      setUserName('');
      setUserEmail('');
      setNotes('');
      setSelectedTimeSlotIso(undefined);
      // setDate(undefined); // Optional: reset date
    },
    onError: (error: Error) => {
      toast({
        title: 'Booking Failed',
        description: error.message || 'An unexpected error occurred. Please try again.',
        variant: 'destructive',
      });
    },
  });

  const handleFormSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    if (!userName || !userEmail || !selectedTimeSlotIso || !date) {
      toast({
        title: 'Missing Information',
        description: 'Please fill in your name, email, and select a date and time slot.',
        variant: 'destructive',
      });
      return;
    }

    const selectedSlot = availableSlotsData?.find(slot => slot.startTime === selectedTimeSlotIso);
    if (!selectedSlot) {
        toast({ title: "Error", description: "Selected time slot not found. Please re-select.", variant: "destructive"});
        return;
    }

    // Placeholder for user_id. In a real app, this would come from auth.
    const placeholderUserId = 1;

    bookingMutation.mutate({
      user_id: placeholderUserId,
      start_time: selectedSlot.startTime,
      end_time: selectedSlot.endTime,
      title: `Consultation with ${userName} - ${consultationType ? consultationTypes.find(ct => ct.id === consultationType)?.name : 'General'}`,
      notes: notes,
    });
  };


	return (
		<section className='py-20 bg-white dark:bg-slate-900'>
			<div className='container mx-auto px-4'>
				<div className='max-w-4xl mx-auto'>
					<div className='text-center mb-12'>
						<span className='inline-block px-4 py-1 mb-4 text-sm font-medium rounded-full bg-red-100 text-red-800'>Book a Consultation</span>
						<h2 className='text-3xl md:text-4xl font-bold mb-4'>Start Your Journey Today</h2>
						<p className='text-gray-600 max-w-2xl mx-auto'>
							Schedule a one-on-one consultation with our expert advisors to discuss your goals and create a personalized plan for success
						</p>
					</div>

					<motion.div
						initial={{opacity: 0, y: 20}}
						animate={{opacity: 1, y: 0}}
						className='bg-white rounded-2xl p-8 shadow-lg border border-gray-100'>
						<form onSubmit={handleFormSubmit} className='space-y-6'>
							<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
								<div>
									<label htmlFor="userName" className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>Full Name</label>
									<Input id="userName" type='text' placeholder='Enter your full name' className='w-full dark:bg-slate-800 dark:text-white' value={userName} onChange={(e) => setUserName(e.target.value)} required />
								</div>
								<div>
									<label htmlFor="userEmail" className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>Email Address</label>
									<Input id="userEmail" type='email' placeholder='Enter your email' className='w-full dark:bg-slate-800 dark:text-white' value={userEmail} onChange={(e) => setUserEmail(e.target.value)} required />
								</div>
							</div>

							<div>
								<label htmlFor="consultationType" className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>Consultation Type</label>
								<Select value={consultationType} onValueChange={setConsultationType} name="consultationType">
									<SelectTrigger className="dark:bg-slate-800 dark:text-white">
										<SelectValue placeholder='Select consultation type' />
									</SelectTrigger>
									<SelectContent className="dark:bg-slate-800">
										{consultationTypes.map((type) => (
											<SelectItem key={type.id} value={type.id} className="dark:hover:bg-slate-700">
												{type.name}
											</SelectItem>
										))}
									</SelectContent>
								</Select>
							</div>

							<div className='grid grid-cols-1 md:grid-cols-2 gap-6'>
								<div>
									<label htmlFor="preferredDate" className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>Preferred Date</label>
									<Calendar
                    id="preferredDate"
                    mode='single'
                    selected={date}
                    onSelect={setDate}
                    className='rounded-md border dark:bg-slate-800 dark:text-white'
                    disabled={(d) => d < new Date(new Date().setHours(0,0,0,0))} // Disable past dates
                  />
								</div>
								<div>
									<label htmlFor="preferredTime" className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>Preferred Time</label>
                  <Select
                    name="preferredTime"
                    value={selectedTimeSlotIso}
                    onValueChange={setSelectedTimeSlotIso}
                    disabled={isLoadingSlots || !!slotsError || !date || !availableSlotsData || availableSlotsData.length === 0}
                  >
										<SelectTrigger className="dark:bg-slate-800 dark:text-white">
											<SelectValue placeholder={
                        !date ? "Select a date first" :
                        isLoadingSlots ? "Loading times..." :
                        slotsError ? "Error loading times" :
                        (!availableSlotsData || availableSlotsData.length === 0) ? "No slots available" :
                        "Select time slot"
                      } />
										</SelectTrigger>
										<SelectContent className="dark:bg-slate-800">
											{availableSlotsData && availableSlotsData.length > 0 && availableSlotsData.map((slot) => (
												<SelectItem key={slot.startTime} value={slot.startTime} className="dark:hover:bg-slate-700">
													{new Date(slot.startTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', timeZone: 'UTC' })}
                          {' - '}
                          {new Date(slot.endTime).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit', timeZone: 'UTC' })}
												</SelectItem>
											))}
										</SelectContent>
									</Select>
								</div>
							</div>

							<div>
								<label htmlFor="notes" className='block text-sm font-medium text-gray-700 dark:text-gray-300 mb-2'>Additional Notes</label>
								<Textarea id="notes" placeholder="Tell us about your goals and any specific areas you'd like to discuss" className='w-full h-32 dark:bg-slate-800 dark:text-white' value={notes} onChange={(e) => setNotes(e.target.value)} />
							</div>

							<Button
                type="submit"
                className='w-full bg-red-700 hover:bg-red-800 text-white'
                disabled={isLoadingSlots || bookingMutation.isLoading || !userName || !userEmail || !selectedTimeSlotIso || !date}
              >
                {bookingMutation.isLoading ? 'Scheduling...' : 'Schedule Consultation'}
              </Button>
						</form>
					</motion.div>

					<div className='mt-8 text-center text-gray-600 dark:text-gray-400'>
						<p>By booking a consultation, you agree to our terms of service and cancellation policy.</p>
					</div>
				</div>
			</div>
		</section>
	);
};
