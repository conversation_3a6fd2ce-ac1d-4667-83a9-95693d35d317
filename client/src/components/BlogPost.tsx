import React from 'react';
import { motion } from 'framer-motion';
import { ArrowLeft, Calendar, User, Tag } from 'lucide-react';

interface BlogPostProps {
  post: {
    id: number;
    title: string;
    content: string;
    author: string;
    date: string;
    category: string;
  };
  onBack: () => void;
}

export const BlogPost: React.FC<BlogPostProps> = ({ post, onBack }) => {
  return (
    <motion.div 
      className="bg-white shadow-lg rounded-lg p-6 hover:shadow-xl transition-shadow duration-300"
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3 }}
    >
      <motion.button 
        onClick={onBack} 
        className="group mb-6 flex items-center text-red-700 hover:text-red-800 transition-colors duration-200"
        whileHover={{ x: -5 }}
        whileTap={{ scale: 0.95 }}
      >
        <ArrowLeft className="w-4 h-4 mr-2 transition-transform duration-200 group-hover:-translate-x-1" />
        Back to list
      </motion.button>

      <motion.h2 
        className="text-2xl md:text-3xl font-bold mb-4 text-gray-900"
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.1 }}
      >
        {post.title}
      </motion.h2>

      <motion.div 
        className="flex flex-wrap items-center gap-4 mb-6"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.2 }}
      >
        <motion.span 
          className="inline-flex items-center bg-red-100 text-red-800 rounded-full px-3 py-1 text-sm font-medium"
          whileHover={{ scale: 1.05 }}
        >
          <Tag className="w-4 h-4 mr-1" />
          {post.category}
        </motion.span>

        <span className="flex items-center text-gray-600 text-sm">
          <User className="w-4 h-4 mr-1" />
          {post.author}
        </span>

        <span className="flex items-center text-gray-600 text-sm">
          <Calendar className="w-4 h-4 mr-1" />
          {post.date}
        </span>
      </motion.div>

      <motion.div
        className="prose prose-lg max-w-none"
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.3 }}
      >
        <p className="text-gray-700 leading-relaxed">{post.content}</p>
      </motion.div>

      <motion.div 
        className="mt-8 pt-6 border-t border-gray-100"
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.4 }}
      >
        <div className="flex items-center justify-between">
          <motion.button
            className="text-red-700 hover:text-red-800 text-sm font-medium"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Share Article
          </motion.button>
          <motion.button
            className="text-red-700 hover:text-red-800 text-sm font-medium"
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
          >
            Save for Later
          </motion.button>
        </div>
      </motion.div>
    </motion.div>
  );
};
