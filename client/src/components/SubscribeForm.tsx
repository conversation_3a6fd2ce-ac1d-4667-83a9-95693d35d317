import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Mail, Loader2, CheckCircle, AlertCircle } from 'lucide-react';
import { Input } from './ui/input';
import { Button } from './ui/button';
import { Alert, AlertDescription } from './ui/alert';

export const SubscribeForm: React.FC = () => {
  const [email, setEmail] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);
  const [error, setError] = useState<string | null>(null);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    setError(null);

    try {
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1500));
      setShowSuccess(true);
      setEmail('');
      setTimeout(() => setShowSuccess(false), 3000);
    } catch (err) {
      setError('Failed to subscribe. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, x: -20 },
    visible: {
      opacity: 1,
      x: 0,
      transition: { duration: 0.3 }
    }
  };

  return (
    <motion.div 
      className="bg-white p-6 rounded-xl shadow-lg border border-gray-100"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div 
        className="flex items-center gap-2 mb-6"
        variants={itemVariants}
      >
        <Mail className="w-5 h-5 text-red-700" />
        <h3 className="text-lg font-semibold text-gray-900">Stay Updated</h3>
      </motion.div>

      <motion.p 
        className="text-gray-600 mb-6 text-sm"
        variants={itemVariants}
      >
        Get the latest IMG resources, tips, and success stories delivered to your inbox.
      </motion.p>

      <motion.form 
        onSubmit={handleSubmit}
        variants={itemVariants}
        className="space-y-4"
      >
        <div className="relative">
          <Input
            type="email"
            value={email}
            onChange={(e) => setEmail(e.target.value)}
            placeholder="Enter your email"
            className="w-full pr-10 focus:ring-2 focus:ring-red-500 transition-shadow duration-200"
            required
            disabled={isSubmitting}
          />
          <Mail className="absolute right-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
        </div>

        <AnimatePresence mode="wait">
          {error && (
            <motion.div
              initial={{ opacity: 0, height: 0 }}
              animate={{ opacity: 1, height: "auto" }}
              exit={{ opacity: 0, height: 0 }}
            >
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            </motion.div>
          )}
        </AnimatePresence>

        <Button 
          type="submit"
          className="w-full bg-red-700 hover:bg-red-800 text-white relative overflow-hidden group"
          disabled={isSubmitting}
        >
          <motion.span
            className="absolute inset-0 bg-white opacity-0 group-hover:opacity-10"
            initial={false}
            animate={{ scale: [1, 2], opacity: [0, 0.1, 0] }}
            transition={{ duration: 0.5, repeat: Infinity }}
          />
          {isSubmitting ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Subscribing...
            </>
          ) : showSuccess ? (
            <>
              <CheckCircle className="mr-2 h-4 w-4" />
              Subscribed!
            </>
          ) : (
            "Subscribe Now"
          )}
        </Button>
      </motion.form>

      <motion.p 
        className="mt-4 text-xs text-gray-500 text-center"
        variants={itemVariants}
      >
        No spam, unsubscribe at any time
      </motion.p>
    </motion.div>
  );
};
