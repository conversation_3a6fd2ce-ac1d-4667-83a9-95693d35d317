import React from 'react';
import {motion} from 'framer-motion';
import {ShoppingCart, ChevronDown} from 'lucide-react';
import {DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger} from './ui/dropdown-menu';
import {Button} from './ui/button';
import ThemeToggle from './ThemeToggle';

const TopBar = () => {
	return (
		<motion.div initial={{y: -50}} animate={{y: 0}} className='bg-red-700 text-white py-2'>
			<div className='container mx-auto px-4'>
				<div className='flex justify-between items-center'>
					<motion.a
						href='mailto:<EMAIL>'
						className='text-sm hover:text-gray-200 transition-colors hidden md:block'
						whileHover={{scale: 1.05}}>
						<EMAIL>
					</motion.a>

					<div className='flex items-center space-x-4'>
						<motion.div whileHover={{scale: 1.05}}>
							<Button variant='ghost' className='text-white hover:text-gray-200 hover:bg-red-800' onClick={() => console.log('My Account clicked')}>
								MY ACCOUNT
							</Button>
						</motion.div>

						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<motion.div whileHover={{scale: 1.05}}>
									<Button variant='ghost' className='text-white hover:text-gray-200 hover:bg-red-800'>
										ENGLISH <ChevronDown className='ml-1 h-4 w-4' />
									</Button>
								</motion.div>
							</DropdownMenuTrigger>
							<DropdownMenuContent>
								<DropdownMenuItem>ENGLISH</DropdownMenuItem>
								<DropdownMenuItem>FRANÇAIS</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>

						<motion.div whileHover={{scale: 1.05}} className='relative'>
							<Button variant='ghost' className='text-white hover:text-gray-200 hover:bg-red-800' onClick={() => console.log('Cart clicked')}>
								<ShoppingCart className='h-5 w-5' />
								<span className='ml-2'>0 ITEMS</span>
							</Button>
						</motion.div>

						<motion.div whileHover={{scale: 1.05}} className='ml-2'>
							<ThemeToggle />
						</motion.div>
					</div>
				</div>
			</div>
		</motion.div>
	);
};

export default TopBar;
