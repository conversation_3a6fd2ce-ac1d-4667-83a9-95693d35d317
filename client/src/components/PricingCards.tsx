import {Button} from './ui/button';
import {<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>} from './ui/card';
import {motion} from 'framer-motion';

const PricingCards = () => {
	return (
		<div className='grid grid-cols-1 md:grid-cols-3 gap-8 px-4 py-8 bg-white dark:bg-slate-900'>
			{/* Basic Package */}
			<Card className='flex flex-col h-full border-2 border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden bg-white dark:bg-gray-800'>
				<CardHeader className='pb-8'>
					<CardTitle className='text-2xl font-bold text-center text-gray-900 dark:text-white'>MCCQE & NAC-OSCE Essentials</CardTitle>
					<p className='text-sm text-gray-600 dark:text-gray-300 text-center mt-2'>Perfect for IMGs starting their MCCQE & NAC-OSCE preparation</p>
					<p className='text-4xl font-bold text-center mt-4 text-gray-900 dark:text-white'>
						$299<span className='text-base font-normal text-gray-500 dark:text-gray-400'>/package</span>
					</p>
				</CardHeader>
				<CardContent className='flex flex-col flex-grow h-full'>
					<ul className='space-y-4 mb-8 flex-grow'>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>2 Full-Length MCCQE & NAC-OSCE Mock Tests</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Basic Study Materials</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Practice Scenarios</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Email Support</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Study Planning Guide</span>
						</li>
					</ul>
					<div className='mt-auto pt-6'>
						<Button className='w-full bg-gray-900 hover:bg-gray-700 dark:bg-gray-100 dark:hover:bg-gray-200 dark:text-gray-900 text-white py-6 text-lg font-semibold rounded-lg transition-colors'>
							Get Started
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Complete Package */}
			<Card className='flex flex-col h-full border-2 border-red-500 dark:border-red-400 rounded-xl overflow-hidden relative bg-white dark:bg-gray-800'>
				<div className='absolute top-4 right-4 bg-red-500 dark:bg-red-600 text-white px-3 py-1 rounded-full text-sm'>Most Popular</div>
				<CardHeader className='pb-8'>
					<CardTitle className='text-2xl font-bold text-center text-gray-900 dark:text-white'>Complete IMG Package</CardTitle>
					<p className='text-sm text-gray-600 dark:text-gray-300 text-center mt-2'>Comprehensive preparation for CASPer and residency</p>
					<p className='text-4xl font-bold text-center mt-4 text-gray-900 dark:text-white'>
						$799<span className='text-base font-normal text-gray-500 dark:text-gray-400'>/package</span>
					</p>
				</CardHeader>
				<CardContent className='flex flex-col flex-grow'>
					<ul className='space-y-4 mb-8 flex-grow'>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>5 Full-Length MCCQE & NAC-OSCE Mock Tests</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>1-on-1 Coaching Sessions</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Personalized Feedback</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Study Partner Matching</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Interview Preparation</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>24/7 Priority Support</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Residency Application Review</span>
						</li>
					</ul>
					<div className='mt-auto pt-6'>
						<Button className='w-full bg-red-700 hover:bg-red-600 dark:bg-red-600 dark:hover:bg-red-500 text-white py-6 text-lg font-semibold rounded-lg transition-colors'>
							Get Started
						</Button>
					</div>
				</CardContent>
			</Card>

			{/* Elite Package */}
			<Card className='flex flex-col h-full border-2 border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden bg-white dark:bg-gray-800'>
				<CardHeader className='pb-8'>
					<CardTitle className='text-2xl font-bold text-center text-gray-900 dark:text-white'>Elite Mentorship</CardTitle>
					<p className='text-sm text-gray-600 dark:text-gray-300 text-center mt-2'>Premium support for your entire IMG journey</p>
					<p className='text-4xl font-bold text-center mt-4 text-gray-900 dark:text-white'>
						$1499<span className='text-base font-normal text-gray-500 dark:text-gray-400'>/package</span>
					</p>
				</CardHeader>
				<CardContent className='flex flex-col flex-grow'>
					<ul className='space-y-4 mb-8 flex-grow'>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Unlimited MCCQE & NAC-OSCE Mock Tests</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Weekly 1-on-1 Coaching</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Comprehensive Study Materials</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>CARMS/ERAS Application Support</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>MMI Interview Training</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Residency Interview Prep</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Personal Success Manager</span>
						</li>
						<li className='flex items-start'>
							<svg className='h-6 w-6 text-green-500 dark:text-green-400 mr-2' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
								<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M5 13l4 4L19 7' />
							</svg>
							<span className='text-gray-700 dark:text-gray-300'>Career Planning Support</span>
						</li>
					</ul>
					<div className='mt-auto pt-6'>
						<Button className='w-full bg-gray-900 hover:bg-gray-700 dark:bg-gray-100 dark:hover:bg-gray-200 dark:text-gray-900 text-white py-6 text-lg font-semibold rounded-lg transition-colors'>
							Get Started
						</Button>
					</div>
				</CardContent>
			</Card>
		</div>
	);
};

export default PricingCards;
