import React from 'react';
import {Button} from './ui/button';
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from './ui/accordion';
import {Users, MessageSquare, Video, Calendar} from 'lucide-react';

interface CoachingService {
	title: string;
	description: string;
	details: {
		title: string;
		description: string;
		price?: string;
		buttonText?: string;
	}[];
	icon: React.ElementType;
}

const CoachingAccordion = () => {
	const coachingServices: CoachingService[] = [
		{
			title: 'Individual & Group Tutoring',
			description: 'Expert-led tutoring sessions tailored to your needs',
			icon: Users,
			details: [
				{
					title: 'Individual Tutoring',
					description: 'One-on-one sessions focused on your specific needs and learning pace.',
					price: 'From $99/hour',
					buttonText: 'Book Individual Session',
				},
				{
					title: 'Group Tutoring',
					description: 'Cost-effective small group sessions with peer learning benefits.',
					price: 'From $49/hour per person',
					buttonText: 'Join Group Session',
				},
				{
					title: 'Available Subjects',
					description: 'NAC OSCE, MCCQE, USMLE Step 1 & 2, Clinical Skills, and more.',
				},
			],
		},
		{
			title: 'Consultations',
			description: 'Personalized guidance for your medical career',
			icon: MessageSquare,
			details: [
				{
					title: 'Career Planning',
					description: 'Strategic planning for your medical career in North America.',
					price: 'From $149/session',
					buttonText: 'Schedule Consultation',
				},
				{
					title: 'Application Review',
					description: 'Comprehensive review of your residency application materials.',
					price: 'From $199/review',
					buttonText: 'Request Review',
				},
				{
					title: 'Mentorship Program',
					description: 'Long-term mentorship with experienced physicians.',
					price: 'From $299/month',
					buttonText: 'Join Program',
				},
			],
		},
		{
			title: 'Online Courses',
			description: 'Comprehensive exam preparation courses',
			icon: Video,
			details: [
				{
					title: 'Self-Paced Learning',
					description: 'Access to recorded lectures, practice questions, and study materials.',
					price: 'From $499',
					buttonText: 'Browse Courses',
				},
				{
					title: 'Live Online Classes',
					description: 'Interactive sessions with expert instructors and peer discussions.',
					price: 'From $699',
					buttonText: 'View Schedule',
				},
				{
					title: 'Course Materials',
					description: 'Comprehensive study guides, practice questions, and assessment tools included with all courses.',
				},
			],
		},
		{
			title: 'Interview Preparation',
			description: 'Comprehensive interview coaching and practice',
			icon: Calendar,
			details: [
				{
					title: 'Mock Interviews',
					description: 'Practice interviews with detailed feedback and coaching.',
					price: 'From $199/session',
					buttonText: 'Schedule Interview',
				},
				{
					title: 'MMI Practice',
					description: 'Multiple Mini Interview scenarios and personalized feedback.',
					price: 'From $249/session',
					buttonText: 'Book MMI Practice',
				},
				{
					title: 'Interview Strategy',
					description: 'Learn effective techniques for answering common questions and presenting yourself professionally.',
				},
			],
		},
	];

	return (
		<div className='max-w-4xl mx-auto p-6'>
			<h2 className='text-3xl font-bold text-center mb-8'>Coaching Services</h2>
			<Accordion type='single' collapsible className='w-full'>
				{coachingServices.map((service, index) => (
					<AccordionItem key={index} value={`item-${index}`}>
						<AccordionTrigger className='text-xl font-semibold'>
							<div className='flex items-center gap-3'>
								<service.icon className='h-6 w-6 text-blue-600' />
								<span>{service.title}</span>
							</div>
						</AccordionTrigger>
						<AccordionContent>
							<div className='space-y-6'>
								<p className='text-gray-600 mb-4'>{service.description}</p>
								{service.details.map((detail, detailIndex) => (
									<div key={detailIndex} className='space-y-2'>
										<h3 className='font-semibold text-lg'>{detail.title}</h3>
										<p className='text-gray-600'>{detail.description}</p>
										{detail.price && <p className='text-blue-600 font-semibold'>{detail.price}</p>}
										{detail.buttonText && (
											<Button variant='outline' className='mt-2' onClick={() => console.log(`Clicked: ${detail.buttonText}`)}>
												{detail.buttonText}
											</Button>
										)}
									</div>
								))}
							</div>
						</AccordionContent>
					</AccordionItem>
				))}
			</Accordion>
		</div>
	);
};

export default CoachingAccordion;
