import React, { useState } from 'react';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { Button } from '../ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../ui/select';
import { Input } from '../ui/input';
import { Checkbox } from '../ui/checkbox';
import { Calendar } from '../ui/calendar';
import { Label } from '../ui/label';
import { toast } from '../ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';

type AvailabilityRuleType = 'recurring' | 'specific_date';

interface AvailabilityFormState {
  ruleType: AvailabilityRuleType;
  dayOfWeek: string; // Store as string "0"-"6" for select, convert to number for API
  specificDate?: Date;
  startTime: string; // "HH:MM"
  endTime: string; // "HH:MM"
  isUnavailable: boolean;
}

const initialFormState: AvailabilityFormState = {
  ruleType: 'recurring',
  dayOfWeek: '1', // Monday
  specificDate: undefined,
  startTime: '09:00',
  endTime: '17:00',
  isUnavailable: false,
};

const weekDays = [
  { label: 'Sunday', value: '0' },
  { label: 'Monday', value: '1' },
  { label: 'Tuesday', value: '2' },
  { label: 'Wednesday', value: '3' },
  { label: 'Thursday', value: '4' },
  { label: 'Friday', value: '5' },
  { label: 'Saturday', value: '6' },
];

const AvailabilityForm: React.FC = () => {
  const [formState, setFormState] = useState<AvailabilityFormState>(initialFormState);
  const queryClient = useQueryClient();

  const handleInputChange = (name: keyof AvailabilityFormState, value: any) => {
    setFormState(prev => ({ ...prev, [name]: value }));
  };

  const handleRuleTypeChange = (value: string) => {
    handleInputChange('ruleType', value as AvailabilityRuleType);
    // Reset conditional fields when type changes
    setFormState(prev => ({
      ...prev,
      ruleType: value as AvailabilityFormState['ruleType'],
      dayOfWeek: value === 'recurring' ? '1' : '', // Default to Monday or clear
      specificDate: value === 'specific_date' ? new Date() : undefined, // Default to today or clear
    }));
  };

  const addRuleMutation = useMutation(
    async (newRule: any) => {
      const response = await fetch('/api/availability', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(newRule),
      });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Failed to add availability rule');
      }
      return response.json();
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['availabilityRules']);
        toast({ title: 'Success', description: 'Availability rule added.' });
        setFormState(initialFormState);
      },
      onError: (error: Error) => {
        toast({
          title: 'Error',
          description: error.message || 'Could not add rule.',
          variant: 'destructive',
        });
      },
    }
  );

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();

    const rulePayload: any = {
      rule_type: formState.ruleType,
      start_time: formState.startTime,
      end_time: formState.endTime,
      is_unavailable: formState.isUnavailable,
    };

    if (formState.ruleType === 'recurring') {
      if (!formState.dayOfWeek) {
        toast({ title: "Validation Error", description: "Day of week is required for recurring rules.", variant: "destructive" });
        return;
      }
      rulePayload.day_of_week = weekDays.find(d => d.value === formState.dayOfWeek)?.label;
    } else if (formState.ruleType === 'specific_date') {
      if (!formState.specificDate) {
        toast({ title: "Validation Error", description: "Specific date is required.", variant: "destructive" });
        return;
      }
      // Format date as YYYY-MM-DD for the API
      const date = formState.specificDate;
      rulePayload.specific_date = `${date.getFullYear()}-${('0' + (date.getMonth() + 1)).slice(-2)}-${('0' + date.getDate()).slice(-2)}`;
    }

    // Basic time validation
    if (formState.startTime >= formState.endTime) {
        toast({ title: "Validation Error", description: "Start time must be before end time.", variant: "destructive" });
        return;
    }

    addRuleMutation.mutate(rulePayload);
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle>Add New Availability Rule</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-6">
          <div>
            <Label htmlFor="ruleType">Rule Type</Label>
            <Select value={formState.ruleType} onValueChange={handleRuleTypeChange}>
              <SelectTrigger id="ruleType">
                <SelectValue placeholder="Select rule type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="recurring">Recurring</SelectItem>
                <SelectItem value="specific_date">Specific Date</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {formState.ruleType === 'recurring' && (
            <div>
              <Label htmlFor="dayOfWeek">Day of Week</Label>
              <Select value={formState.dayOfWeek} onValueChange={(val) => handleInputChange('dayOfWeek', val)}>
                <SelectTrigger id="dayOfWeek">
                  <SelectValue placeholder="Select day" />
                </SelectTrigger>
                <SelectContent>
                  {weekDays.map(day => (
                    <SelectItem key={day.value} value={day.value}>{day.label}</SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}

          {formState.ruleType === 'specific_date' && (
            <div className="flex flex-col space-y-2">
              <Label htmlFor="specificDate">Specific Date</Label>
               <Calendar
                  mode="single"
                  selected={formState.specificDate}
                  onSelect={(date) => handleInputChange('specificDate', date)}
                  className="rounded-md border"
                  initialFocus
                />
            </div>
          )}

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <Label htmlFor="startTime">Start Time</Label>
              <Input
                id="startTime"
                type="time"
                value={formState.startTime}
                onChange={(e) => handleInputChange('startTime', e.target.value)}
                required
              />
            </div>
            <div>
              <Label htmlFor="endTime">End Time</Label>
              <Input
                id="endTime"
                type="time"
                value={formState.endTime}
                onChange={(e) => handleInputChange('endTime', e.target.value)}
                required
              />
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="isUnavailable"
              checked={formState.isUnavailable}
              onCheckedChange={(checked) => handleInputChange('isUnavailable', checked)}
            />
            <Label htmlFor="isUnavailable" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
              Mark as Unavailable
            </Label>
          </div>

          <Button type="submit" disabled={addRuleMutation.isLoading}>
            {addRuleMutation.isLoading ? 'Adding...' : 'Add Rule'}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
};

export default AvailabilityForm;
