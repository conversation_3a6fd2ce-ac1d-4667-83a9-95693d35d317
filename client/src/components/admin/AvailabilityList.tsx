import React from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../ui/table';
import { Button } from '../ui/button';
import { Badge } from '../ui/badge';
import { toast } from '../ui/use-toast';
import { Card, CardContent, CardHeader, CardTitle } from '../ui/card';
import { AlertTriangle, Loader2 } from 'lucide-react'; // Icons

interface AvailabilityRule {
  id: number;
  rule_type: 'recurring' | 'specific_date';
  day_of_week?: 'Sunday' | 'Monday' | 'Tuesday' | 'Wednesday' | 'Thursday' | 'Friday' | 'Saturday' | null;
  specific_date?: string | null; // YYYY-MM-DD
  start_time: string; // HH:MM:SS
  end_time: string; // HH:MM:SS
  is_unavailable?: boolean;
  created_at: string;
  updated_at: string;
}

const weekDayMap: { [key: number]: AvailabilityRule['day_of_week'] } = {
  0: 'Sunday', 1: 'Monday', 2: 'Tuesday', 3: 'Wednesday', 4: 'Thursday', 5: 'Friday', 6: 'Saturday'
};
const weekDayStringToNumber: { [key: string]: number } = {
    'Sunday':0, 'Monday':1, 'Tuesday':2, 'Wednesday':3, 'Thursday':4, 'Friday':5, 'Saturday':6
};


const AvailabilityList: React.FC = () => {
  const queryClient = useQueryClient();

  const fetchAvailabilityRules = async (): Promise<AvailabilityRule[]> => {
    const response = await fetch('/api/availability');
    if (!response.ok) {
      const errorData = await response.json();
      throw new Error(errorData.message || 'Failed to fetch availability rules');
    }
    return response.json();
  };

  const { data: rules, isLoading, error } = useQuery<AvailabilityRule[], Error>(
    ['availabilityRules'],
    fetchAvailabilityRules
  );

  const deleteRuleMutation = useMutation(
    async (ruleId: number) => {
      const response = await fetch(`/api/availability/${ruleId}`, { method: 'DELETE' });
      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || `Failed to delete rule ${ruleId}`);
      }
      // DELETE often returns 200/204 with no content or a success message
      // We don't strictly need to parse JSON here unless the API guarantees it for errors too
      if (response.status !== 204 && response.status !== 200)  { // Check if response is not OK
          const errorData = await response.json().catch(() => ({ message: "Unknown error occurred" })); // try to parse error, or default
          throw new Error(errorData.message || `Failed to delete rule ${ruleId}`);
      }
      return ruleId; // Return ruleId or a success indicator if needed
    },
    {
      onSuccess: () => {
        queryClient.invalidateQueries(['availabilityRules']);
        toast({ title: 'Success', description: 'Availability rule deleted.' });
      },
      onError: (error: Error) => {
        toast({
          title: 'Error',
          description: error.message || 'Could not delete rule.',
          variant: 'destructive',
        });
      },
    }
  );

  const formatTime = (timeStr: string) => {
    if (!timeStr) return '';
    const [hours, minutes] = timeStr.split(':');
    return `${hours}:${minutes}`; // Format to HH:MM
  };

  const formatDate = (dateStr?: string | null) => {
    if (!dateStr) return 'N/A';
    // Assuming dateStr is YYYY-MM-DD from specific_date or full ISO from created_at/updated_at
    const date = new Date(dateStr);
     // Adjust for timezone if specific_date is coming as YYYY-MM-DD (which is treated as UTC midnight)
    if (dateStr.length === 10) { // Likely YYYY-MM-DD
        const [year, month, day] = dateStr.split('-').map(Number);
        return new Date(year, month - 1, day).toLocaleDateString(); // Use local timezone for display
    }
    return date.toLocaleDateString(); // For full timestamps, this is fine
  };


  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-10">
        <Loader2 className="h-8 w-8 animate-spin text-primary" />
        <span className="ml-2 text-lg">Loading availability rules...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center py-10 text-red-600">
        <AlertTriangle className="h-10 w-10 mb-3" />
        <p className="text-xl font-semibold">Error loading availability rules</p>
        <p>{error.message}</p>
      </div>
    );
  }

  if (!rules || rules.length === 0) {
    return (
      <Card>
        <CardHeader><CardTitle>Availability Rules</CardTitle></CardHeader>
        <CardContent><p className="text-center text-gray-500 py-5">No availability rules found.</p></CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Current Availability Rules</CardTitle>
      </CardHeader>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Type</TableHead>
              <TableHead>Day/Date</TableHead>
              <TableHead>Start Time</TableHead>
              <TableHead>End Time</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Actions</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {rules.map((rule) => (
              <TableRow key={rule.id}>
                <TableCell className="capitalize">{rule.rule_type.replace('_', ' ')}</TableCell>
                <TableCell>
                  {rule.rule_type === 'recurring'
                    ? rule.day_of_week
                    : formatDate(rule.specific_date)}
                </TableCell>
                <TableCell>{formatTime(rule.start_time)}</TableCell>
                <TableCell>{formatTime(rule.end_time)}</TableCell>
                <TableCell>
                  {rule.is_unavailable ? (
                    <Badge variant="destructive">Unavailable</Badge>
                  ) : (
                    <Badge variant="secondary">Available</Badge>
                  )}
                </TableCell>
                <TableCell>
                  <Button
                    variant="destructive"
                    size="sm"
                    onClick={() => deleteRuleMutation.mutate(rule.id)}
                    disabled={deleteRuleMutation.isLoading && deleteRuleMutation.variables === rule.id}
                  >
                    {deleteRuleMutation.isLoading && deleteRuleMutation.variables === rule.id ? <Loader2 className="h-4 w-4 animate-spin mr-1" /> : null}
                    Delete
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </CardContent>
    </Card>
  );
};

export default AvailabilityList;
