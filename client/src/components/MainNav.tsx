import React from 'react';
import {motion} from 'framer-motion';
import {<PERSON>} from 'react-router-dom';
import {Star, ChevronDown} from 'lucide-react';
import {
	NavigationMenu,
	NavigationMenuContent,
	NavigationMenuItem,
	NavigationMenuLink,
	NavigationMenuList,
	NavigationMenuTrigger,
} from './ui/navigation-menu';
import {Button} from './ui/button';
import {cn} from '../lib/utils';

const examPrepLinks = [
	{
		title: 'MCCQE',
		href: '/courses',
		description: 'Comprehensive preparation for the Medical Council of Canada Qualifying Examination.',
	},
	{
		title: 'NAC-OSCE',
		href: '/courses',
		description: 'Prepare for the National Assessment Collaboration Examination.',
	},
	{
		title: 'Mock Exams',
		href: '/mock-exams',
		description: 'Full-length practice tests with detailed explanations.',
	},
	{
		title: 'Question Bank',
		href: '/question-bank',
		description: 'Extensive collection of practice questions and explanations.',
	},
];

const professionalDevLinks = [
	{
		title: 'One-on-One Coaching',
		href: '/coaching/individual',
		description: 'Personalized coaching sessions tailored to your needs.',
	},
	{
		title: 'Interview Prep',
		href: '/interview-prep',
		description: 'Comprehensive interview preparation and practice.',
	},
	{
		title: 'CASPER® Prep',
		href: '/casper-prep',
		description: 'Computer-Based Assessment for Sampling Personal Characteristics.',
	},
	{
		title: 'Application Review',
		href: '/coaching/application',
		description: 'Expert review of your residency application materials.',
	},
];

const ListItem = React.forwardRef<React.ElementRef<'a'>, React.ComponentPropsWithoutRef<'a'>>(({className, title, children, ...props}, ref) => {
	return (
		<li>
			<NavigationMenuLink asChild>
				<a
					ref={ref}
					className={cn(
						'block select-none space-y-1 rounded-md p-3 leading-none no-underline outline-none transition-colors hover:bg-red-50 hover:text-red-700',
						className
					)}
					{...props}>
					<div className='text-sm font-medium leading-none'>{title}</div>
					<p className='line-clamp-2 text-sm leading-snug text-gray-600'>{children}</p>
				</a>
			</NavigationMenuLink>
		</li>
	);
});
ListItem.displayName = 'ListItem';

const MainNav = () => {
	return (
		<NavigationMenu className='hidden md:flex w-full'>
			<NavigationMenuList className='gap-4 w-full items-center'>
				<NavigationMenuItem>
					<NavigationMenuTrigger className='text-gray-700 hover:text-red-700 hover:bg-red-50'>EXAM PREP</NavigationMenuTrigger>
					<NavigationMenuContent>
						<ul className='grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2'>
							{examPrepLinks.map((link) => (
								<ListItem key={link.title} title={link.title} href={link.href}>
									{link.description}
								</ListItem>
							))}
						</ul>
					</NavigationMenuContent>
				</NavigationMenuItem>

				<NavigationMenuItem>
					<NavigationMenuTrigger className='text-gray-700 hover:text-red-700 hover:bg-red-50'>PROFESSIONAL DEV</NavigationMenuTrigger>
					<NavigationMenuContent>
						<ul className='grid w-[400px] gap-3 p-4 md:w-[500px] md:grid-cols-2'>
							{professionalDevLinks.map((link) => (
								<ListItem key={link.title} title={link.title} href={link.href}>
									{link.description}
								</ListItem>
							))}
						</ul>
					</NavigationMenuContent>
				</NavigationMenuItem>

				<NavigationMenuItem>
					<Link to='/study-partner' className='px-4 py-2 text-gray-700 hover:text-red-700 hover:bg-red-50 rounded-md transition duration-300'>
						STUDY PARTNER
					</Link>
				</NavigationMenuItem>

				<NavigationMenuItem>
					<Link to='/courses' className='px-4 py-2 text-gray-700 hover:text-red-700 hover:bg-red-50 rounded-md transition duration-300'>
						COURSES
					</Link>
				</NavigationMenuItem>

				<NavigationMenuItem>
					<Link to='/packages' className='px-4 py-2 text-gray-700 hover:text-red-700 hover:bg-red-50 rounded-md transition duration-300'>
						PACKAGES
					</Link>
				</NavigationMenuItem>

				<NavigationMenuItem>
					<Link to='/about' className='px-4 py-2 text-gray-700 hover:text-red-700 hover:bg-red-50 rounded-md transition duration-300'>
						ABOUT
					</Link>
				</NavigationMenuItem>

				<NavigationMenuItem>
					<Button className='bg-red-700 hover:bg-red-800 text-white ml-2' onClick={() => console.log('Book a Session clicked')}>
						BOOK A SESSION
					</Button>
				</NavigationMenuItem>
			</NavigationMenuList>
		</NavigationMenu>
	);
};

export default MainNav;
