import {motion, useInView} from 'framer-motion';
import {useRef} from 'react';
import {Check} from 'lucide-react';
import {Button} from './ui/button';

const pricingPlans = [
	{
		name: 'MCCQE & NAC-OSCE Essentials',
		price: '299',
		description: 'Perfect for IMGs starting their MCCQE & NAC-OSCE preparation',
		features: ['2 Full-Length MCCQE & NAC-OSCE Mock Tests', 'Basic Study Materials', 'Practice Scenarios', 'Email Support', 'Study Planning Guide'],
		popular: false,
	},
	{
		name: 'Complete IMG Package',
		price: '799',
		description: 'Comprehensive preparation for CASPer and residency',
		features: [
			'5 Full-Length MCCQE & NAC-OSCE Mock Tests',
			'1-on-1 Coaching Sessions',
			'Personalized Feedback',
			'Study Partner Matching',
			'Interview Preparation',
			'24/7 Priority Support',
			'Residency Application Review',
		],
		popular: true,
	},
	{
		name: 'Elite Mentorship',
		price: '1499',
		description: 'Premium support for your entire IMG journey',
		features: [
			'Unlimited MCCQE & NAC-OSCE Mock Tests',
			'Weekly 1-on-1 Coaching',
			'Comprehensive Study Materials',
			'CARMS/ERAS Application Support',
			'MMI Interview Training',
			'Residency Interview Prep',
			'Personal Success Manager',
			'Career Planning Support',
		],
		popular: false,
	},
];

export const ServicePricing = () => {
	const ref = useRef(null);
	const isInView = useInView(ref, {once: true, margin: '-100px'});

	const containerVariants = {
		hidden: {opacity: 0},
		visible: {
			opacity: 1,
			transition: {
				staggerChildren: 0.2,
				delayChildren: 0.3,
			},
		},
	};

	const cardVariants = {
		hidden: {opacity: 0, y: 50},
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'spring',
				stiffness: 100,
				damping: 15,
			},
		},
	};

	const checkmarkVariants = {
		hidden: {pathLength: 0, opacity: 0},
		visible: {
			pathLength: 1,
			opacity: 1,
			transition: {
				duration: 0.5,
				ease: 'easeInOut',
			},
		},
	};
	return (
		<section className='py-20 bg-gradient-to-b from-white to-gray-50 dark:from-slate-900 dark:to-slate-800 overflow-hidden'>
			<motion.div
				ref={ref}
				className='container mx-auto px-4'
				variants={containerVariants}
				initial='hidden'
				animate={isInView ? 'visible' : 'hidden'}>
				<motion.div
					className='text-center mb-16'
					initial={{opacity: 0, y: -20}}
					animate={isInView ? {opacity: 1, y: 0} : {opacity: 0, y: -20}}
					transition={{duration: 0.6}}>
					<motion.span
						className='inline-block px-4 py-1 mb-4 text-sm font-medium rounded-full bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200'
						whileHover={{scale: 1.05}}
						whileTap={{scale: 0.95}}>
						Pricing Plans
					</motion.span>
					<motion.h2
						className='text-3xl md:text-4xl font-bold mb-4 text-gray-900 dark:text-white'
						initial={{opacity: 0, y: 20}}
						animate={isInView ? {opacity: 1, y: 0} : {opacity: 0, y: 20}}
						transition={{duration: 0.6, delay: 0.2}}>
						Choose Your Success Path
					</motion.h2>
					<motion.p
						className='text-gray-600 dark:text-gray-300 max-w-2xl mx-auto'
						initial={{opacity: 0}}
						animate={isInView ? {opacity: 1} : {opacity: 0}}
						transition={{duration: 0.6, delay: 0.3}}>
						Select the package that best fits your needs and start your journey to becoming a licensed physician
					</motion.p>
				</motion.div>

				<div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
					{pricingPlans.map((plan, index) => (
						<motion.div
							key={plan.name}
							variants={cardVariants}
							whileHover={{
								y: -10,
								transition: {duration: 0.2},
							}}
							className={`relative bg-white dark:bg-gray-800 rounded-2xl p-8 shadow-lg border ${plan.popular ? 'border-red-500 dark:border-red-400' : 'border-gray-100 dark:border-gray-700'} transform-gpu`}>
							{plan.popular && (
								<motion.div
									className='absolute -top-4 left-1/2 transform -translate-x-1/2'
									initial={{scale: 0}}
									animate={isInView ? {scale: 1} : {scale: 0}}
									transition={{delay: 0.5, type: 'spring', stiffness: 200}}>
									<motion.span
										className='bg-red-500 dark:bg-red-600 text-white px-4 py-1 rounded-full text-sm font-medium inline-block'
										whileHover={{scale: 1.05}}
										whileTap={{scale: 0.95}}>
										Most Popular
									</motion.span>
								</motion.div>
							)}

							<div className='text-center mb-8'>
								<h3 className='text-xl font-bold mb-2 text-gray-900 dark:text-white'>{plan.name}</h3>
								<p className='text-gray-600 dark:text-gray-300 mb-4'>{plan.description}</p>
								<div className='flex items-center justify-center'>
									<span className='text-3xl font-bold text-gray-900 dark:text-white'>${plan.price}</span>
									<span className='text-gray-500 dark:text-gray-400 ml-2'>/package</span>
								</div>
							</div>

							<ul className='space-y-4 mb-8'>
								{plan.features.map((feature) => (
									<li key={feature} className='flex items-center'>
										<motion.div initial='hidden' animate='visible' className='relative w-5 h-5 mr-3 flex-shrink-0'>
											<motion.svg
												viewBox='0 0 24 24'
												className='w-5 h-5 text-green-500 dark:text-green-400'
												fill='none'
												stroke='currentColor'
												strokeWidth='2'>
												<motion.path d='M20 6L9 17l-5-5' variants={checkmarkVariants} />
											</motion.svg>
										</motion.div>
										<span className='text-gray-600 dark:text-gray-300'>{feature}</span>
									</li>
								))}
							</ul>

							<motion.div whileHover={{scale: 1.02}} whileTap={{scale: 0.98}}>
								<Button
									className={`w-full ${
										plan.popular
											? 'bg-red-700 hover:bg-red-800 dark:bg-red-600 dark:hover:bg-red-700'
											: 'bg-gray-800 hover:bg-gray-900 dark:bg-gray-100 dark:hover:bg-gray-200 dark:text-gray-900'
									} text-white relative overflow-hidden group`}>
									<motion.span
										className='absolute inset-0 bg-white opacity-0 group-hover:opacity-10'
										initial={false}
										animate={{scale: [1, 2], opacity: [0, 0.1, 0]}}
										transition={{duration: 0.5, repeat: Infinity}}
									/>
									Get Started
								</Button>
							</motion.div>
						</motion.div>
					))}
				</div>

				<motion.div className='mt-12 text-center' initial={{opacity: 0}} animate={isInView ? {opacity: 1} : {opacity: 0}} transition={{delay: 0.8}}>
					<p className='text-gray-600 dark:text-gray-300'>
						Need a custom package?{' '}
						<motion.button
							className='text-red-700 dark:text-red-500 font-medium relative inline-block'
							whileHover={{scale: 1.05}}
							whileTap={{scale: 0.95}}>
							<span>Contact us</span>
							<motion.span
								className='absolute bottom-0 left-0 w-full h-0.5 bg-red-700 dark:bg-red-500'
								initial={{scaleX: 0}}
								whileHover={{scaleX: 1}}
								transition={{duration: 0.2}}
							/>
						</motion.button>{' '}
						for personalized solutions.
					</p>
				</motion.div>
			</motion.div>
		</section>
	);
};
