import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { Filter } from 'lucide-react';

interface CategoryFilterProps {
  onSelectCategory: (category: string | null) => void;
}

const categories = [
  { id: 'all', name: 'All Posts' },
  { id: 'residency', name: 'Residency Tips' },
  { id: 'exam', name: 'Exam Prep' },
  { id: 'news', name: 'Healthcare News' }
];

export const CategoryFilter: React.FC<CategoryFilterProps> = ({ onSelectCategory }) => {
  const [activeCategory, setActiveCategory] = useState<string>('all');

  const handleCategoryClick = (categoryId: string) => {
    setActiveCategory(categoryId);
    onSelectCategory(categoryId === 'all' ? null : categoryId);
  };

  const containerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, scale: 0.8 },
    visible: {
      opacity: 1,
      scale: 1,
      transition: { duration: 0.3 }
    }
  };
  return (
    <motion.div 
      className="mb-8"
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <motion.div 
        className="flex items-center mb-4"
        variants={itemVariants}
      >
        <Filter className="w-5 h-5 text-red-700 mr-2" />
        <h3 className="text-lg font-semibold text-gray-900">Filter by Category</h3>
      </motion.div>

      <motion.div 
        className="flex flex-wrap gap-3"
        variants={containerVariants}
      >
        {categories.map((category) => (
          <motion.button
            key={category.id}
            variants={itemVariants}
            whileHover={{ scale: 1.05 }}
            whileTap={{ scale: 0.95 }}
            onClick={() => handleCategoryClick(category.id)}
            className={`
              px-4 py-2 rounded-full text-sm font-medium transition-all duration-200
              ${activeCategory === category.id
                ? 'bg-red-100 text-red-800 shadow-md'
                : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }
              relative overflow-hidden group
            `}
          >
            <motion.span
              className="absolute inset-0 bg-gradient-to-r from-red-50 to-transparent opacity-0 group-hover:opacity-100"
              initial={false}
              animate={{ scale: [1, 2], opacity: [0, 0.1, 0] }}
              transition={{ duration: 0.5, repeat: Infinity }}
            />
            <span className="relative z-10">{category.name}</span>
            {activeCategory === category.id && (
              <motion.div
                className="absolute bottom-0 left-0 right-0 h-0.5 bg-red-500"
                layoutId="activeCategory"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.3 }}
              />
            )}
          </motion.button>
        ))}
      </motion.div>
    </motion.div>
  );
};
