import {motion, useInView} from 'framer-motion';
import {useRef} from 'react';
import {Brain, Users, FileCheck2, MessageCircle, Clock, Target} from 'lucide-react';

const services = [
	{
		icon: Brain,
		title: 'Scenario Analysis Training',
		description: 'Learn proven techniques to analyze and respond to MCCQE & NAC-OSCE scenarios effectively',
	},
	{
		icon: Users,
		title: 'Small Group Practice',
		description: 'Interactive sessions with expert coaches to improve your situational judgment',
	},
	{
		icon: FileCheck2,
		title: 'Full-Length Simulations',
		description: 'Practice with timed, realistic MCCQE & NAC-OSCE exam simulations and detailed feedback',
	},
	{
		icon: MessageCircle,
		title: '1-on-1 Coaching',
		description: 'Personalized guidance to strengthen your ethical decision-making skills',
	},
	{
		icon: Clock,
		title: 'Time Management Skills',
		description: 'Master strategies to effectively manage time during the MCCQE & NAC-OSCE exams',
	},
	{
		icon: Target,
		title: 'Targeted Practice',
		description: 'Focus on specific competencies assessed in the MCCQE & NAC-OSCE exams',
	},
];

export const Services = () => {
	const ref = useRef(null);
	const isInView = useInView(ref, {once: true, margin: '-100px'});

	const containerVariants = {
		hidden: {opacity: 0},
		visible: {
			opacity: 1,
			transition: {
				staggerChildren: 0.2,
				delayChildren: 0.3,
			},
		},
	};

	const itemVariants = {
		hidden: {opacity: 0, y: 20},
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.5,
				ease: 'easeOut',
			},
		},
	};

	const iconVariants = {
		hidden: {scale: 0, rotate: -180},
		visible: {
			scale: 1,
			rotate: 0,
			transition: {
				type: 'spring',
				stiffness: 260,
				damping: 20,
			},
		},
		hover: {
			scale: 1.1,
			rotate: 5,
			transition: {
				duration: 0.3,
			},
		},
	};

	return (
		<section className='py-20 bg-gradient-to-b from-white to-red-50 overflow-hidden'>
			<motion.div
				ref={ref}
				className='container mx-auto px-4'
				variants={containerVariants}
				initial='hidden'
				animate={isInView ? 'visible' : 'hidden'}>
				<motion.div
					className='text-center mb-16'
					initial={{opacity: 0, y: -20}}
					animate={isInView ? {opacity: 1, y: 0} : {opacity: 0, y: -20}}
					transition={{duration: 0.6}}>
					<motion.span
						className='inline-block px-4 py-1 mb-4 text-sm font-medium rounded-full bg-red-100 text-red-800'
						whileHover={{scale: 1.05}}
						whileTap={{scale: 0.95}}>
						Comprehensive MCCQE & NAC-OSCE Preparation
					</motion.span>
					<motion.h2
						className='text-3xl md:text-4xl font-bold mb-4'
						initial={{opacity: 0, y: 20}}
						animate={isInView ? {opacity: 1, y: 0} : {opacity: 0, y: 20}}
						transition={{duration: 0.6, delay: 0.2}}>
						Expert-Led MCCQE & NAC-OSCE Services
					</motion.h2>
					<motion.p
						className='text-gray-600 max-w-2xl mx-auto'
						initial={{opacity: 0}}
						animate={isInView ? {opacity: 1} : {opacity: 0}}
						transition={{duration: 0.6, delay: 0.3}}>
						Tailored programs designed to help you excel in every aspect of the MCCQE & NAC-OSCE exams
					</motion.p>
				</motion.div>

				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
					{services.map((service, index) => (
						<motion.div
							key={service.title}
							variants={itemVariants}
							whileHover={{
								y: -5,
								transition: {duration: 0.2},
							}}
							className='bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 relative group'>
							<motion.div variants={iconVariants} whileHover='hover' className='relative z-10'>
								<service.icon className='h-12 w-12 text-red-600 mb-6' />
							</motion.div>
							<motion.div
								className='absolute inset-0 bg-gradient-to-br from-red-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl'
								initial={false}
								animate={{opacity: 0}}
								whileHover={{opacity: 1}}
							/>
							<motion.h3
								className='text-xl font-semibold mb-2 relative z-10'
								variants={{
									hover: {x: 5},
								}}
								whileHover='hover'>
								{service.title}
							</motion.h3>
							<p className='text-gray-600 relative z-10'>{service.description}</p>
							<motion.div
								className='absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity'
								initial={{scale: 0}}
								whileHover={{scale: 1.1}}>
								<svg className='w-6 h-6 text-red-600' fill='none' viewBox='0 0 24 24' stroke='currentColor'>
									<path strokeLinecap='round' strokeLinejoin='round' strokeWidth={2} d='M17 8l4 4m0 0l-4 4m4-4H3' />
								</svg>
							</motion.div>
						</motion.div>
					))}
				</div>
			</motion.div>
		</section>
	);
};
