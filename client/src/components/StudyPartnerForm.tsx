import {zodResolver} from '@hookform/resolvers/zod';
import {useForm} from 'react-hook-form';
import * as z from 'zod';
import {Button} from './ui/button';
import {Form, FormControl, FormField, FormItem, FormLabel, FormMessage} from './ui/form';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from './ui/select';
import {useToast} from './ui/use-toast';
import {RadioGroup, RadioGroupItem} from './ui/radio-group';
import {Checkbox} from './ui/checkbox';

const formSchema = z.object({
	examType: z.string(),
	targetExamDate: z.string(),
	timeZone: z.string(),
	dailyAvailability: z.string(),
	language: z.string(),
	preferredStudySchedule: z.string(),
	currentProgress: z.string(),
	preferredStudyMethods: z.array(z.string()),
	studyStyle: z.string(),
	groupSizePreference: z.string().min(1, 'Please select a group size preference'),
	genderPreference: z.enum(['male', 'female'], {
		required_error: 'Please select a gender preference',
	}),
	preferredCommunication: z.string().optional(),
	yearsOfExperience: z.number().optional(),
});

export const StudyPartnerForm = () => {
	const {toast} = useToast();
	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			examType: '',
			targetExamDate: '',
			timeZone: '',
			dailyAvailability: '',
			language: '',
			preferredStudySchedule: '',
			currentProgress: '',
			preferredStudyMethods: [],
			studyStyle: '',
			groupSizePreference: '',
			genderPreference: undefined,
			preferredCommunication: '',
			yearsOfExperience: undefined,
		},
	});

	const onSubmit = async (values: z.infer<typeof formSchema>) => {
		try {
			const response = await fetch('/api/study-partner', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify(values),
			});

			const data = await response.json();

			if (!response.ok) {
				throw new Error(data.error || 'Failed to submit study partner profile');
			}

			console.log(data);

			toast({
				title: 'Profile Submitted!',
				description: 'Your study partner profile has been created successfully.',
			});

			// Optionally, you can reset the form here
			form.reset();
		} catch (error) {
			console.error('Error submitting study partner profile:', error);
			toast({
				title: 'Submission Failed',
				description: error instanceof Error ? error.message : 'There was an error submitting your profile. Please try again.',
				variant: 'destructive',
			});
		}
	};

	return (
		<Form {...form}>
			<form onSubmit={form.handleSubmit(onSubmit)} className='max-w-2xl mx-auto space-y-8'>
				<FormField
					control={form.control}
					name='examType'
					render={({field}) => (
						<FormItem>
							<FormLabel>Exam Type</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder='Select your exam type' />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value='MCCQE'>MCCQE</SelectItem>
									<SelectItem value='usmle1'>USMLE Step 1</SelectItem>
									<SelectItem value='nacosce'>NAC OSCE</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='targetExamDate'
					render={({field}) => (
						<FormItem>
							<FormLabel>Exam Timeline</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder='Select your exam timeline' />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value='3months'>Within 3 months</SelectItem>
									<SelectItem value='6months'>Within 6 months</SelectItem>
									<SelectItem value='12months'>Within 12 months</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='timeZone'
					render={({field}) => (
						<FormItem>
							<FormLabel>Time Zone</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder='Select your time zone' />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value='EST'>Eastern Time (EST)</SelectItem>
									<SelectItem value='CST'>Central Time (CST)</SelectItem>
									<SelectItem value='MST'>Mountain Time (MST)</SelectItem>
									<SelectItem value='PST'>Pacific Time (PST)</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='dailyAvailability'
					render={({field}) => (
						<FormItem>
							<FormLabel>Daily study availability (hours per day)</FormLabel>
							<FormControl>
								<input type='number' {...field} className='w-full p-2 border rounded' onChange={(e) => field.onChange(e.target.value)} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='language'
					render={({field}) => (
						<FormItem>
							<FormLabel>Preferred Language</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder='Select your preferred language' />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value='english'>English</SelectItem>
									<SelectItem value='french'>French</SelectItem>
									<SelectItem value='spanish'>Spanish</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='preferredStudySchedule'
					render={({field}) => (
						<FormItem>
							<FormLabel>Preferred Study Schedule</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder='Select your preferred schedule' />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value='morning'>Morning</SelectItem>
									<SelectItem value='afternoon'>Afternoon</SelectItem>
									<SelectItem value='evening'>Evening</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='currentProgress'
					render={({field}) => (
						<FormItem>
							<FormLabel>Current Progress in Exam Preparation</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder='Select your current progress' />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value='beginner'>Beginner</SelectItem>
									<SelectItem value='intermediate'>Intermediate</SelectItem>
									<SelectItem value='advanced'>Advanced</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='preferredStudyMethods'
					render={({field}) => (
						<FormItem className='space-y-3'>
							<FormLabel>Preferred Study Methods</FormLabel>
							<FormControl>
								<div className='grid grid-cols-2 gap-4'>
									{['Question Banks', 'Video Lectures', 'Flashcards', 'Mock Exams'].map((method) => (
										<div key={method} className='flex items-center space-x-2'>
											<Checkbox
												id={`method-${method}`}
												checked={field.value?.includes(method)}
												onCheckedChange={(checked) => {
													const newValue = checked ? [...(field.value || []), method] : (field.value || []).filter((m) => m !== method);
													field.onChange(newValue);
												}}
											/>
											<label
												htmlFor={`method-${method}`}
												className='text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70'>
												{method}
											</label>
										</div>
									))}
								</div>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='studyStyle'
					render={({field}) => (
						<FormItem>
							<FormLabel>Study Style</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder='Select your study style' />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value='competitive'>Competitive</SelectItem>
									<SelectItem value='collaborative'>Collaborative</SelectItem>
									<SelectItem value='quiet'>Quiet</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='groupSizePreference'
					render={({field}) => (
						<FormItem>
							<FormLabel>Group Size Preference</FormLabel>
							<FormControl>
								<input
									type='number'
									min='1'
									max='10'
									{...field}
									className='w-full p-2 border rounded'
									onChange={(e) => field.onChange(Number(e.target.value))}
								/>
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<FormField
					control={form.control}
					name='genderPreference'
					render={({field}) => (
						<FormItem>
							<FormLabel>Gender Preference</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder='Select preferred gender' />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value='Male'>Male</SelectItem>
									<SelectItem value='Female'>Female</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name='preferredCommunication'
					render={({field}) => (
						<FormItem>
							<FormLabel>Preferred Communication Platform</FormLabel>
							<Select onValueChange={field.onChange} defaultValue={field.value}>
								<FormControl>
									<SelectTrigger>
										<SelectValue placeholder='Select a platform' />
									</SelectTrigger>
								</FormControl>
								<SelectContent>
									<SelectItem value='discord'>Discord</SelectItem>
									<SelectItem value='zoom'>Zoom</SelectItem>
									<SelectItem value='skype'>Skype</SelectItem>
									<SelectItem value='google meet'>Google Meet</SelectItem>
								</SelectContent>
							</Select>
							<FormMessage />
						</FormItem>
					)}
				/>
				<FormField
					control={form.control}
					name='yearsOfExperience'
					render={({field}) => (
						<FormItem>
							<FormLabel>Years of Experience</FormLabel>
							<FormControl>
								<input type='number' {...field} className='w-full p-2 border rounded' onChange={(e) => field.onChange(Number(e.target.value))} />
							</FormControl>
							<FormMessage />
						</FormItem>
					)}
				/>

				<Button type='submit' className='w-full'>
					Find Study Partner
				</Button>
			</form>
		</Form>
	);
};
