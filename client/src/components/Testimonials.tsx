import {motion, useInView} from 'framer-motion';
import {Star, Quote} from 'lucide-react';
import {useRef} from 'react';

const testimonials = [
	{
		name: 'Dr. <PERSON>',
		role: 'IMG from Australia',
		content:
			'The MCCQE & NAC-OSCE exam preparation was exceptional. The personalized coaching and practice scenarios helped me understand exactly what evaluators were looking for.',
		rating: 5,
	},
	{
		name: 'Dr. <PERSON>',
		role: 'IMG from India',
		content:
			'The mock interviews and feedback sessions were invaluable. I felt much more confident during my actual residency interviews thanks to this preparation.',
		rating: 5,
	},
	{
		name: 'Dr. <PERSON>',
		role: 'IMG from Brazil',
		content:
			"The study partner matching system helped me find the perfect study buddy. We've been preparing together for months and both matched into our desired programs!",
		rating: 5,
	},
];

export const Testimonials = () => {
	const ref = useRef(null);
	const isInView = useInView(ref, {once: true, margin: '-100px'});

	const containerVariants = {
		hidden: {opacity: 0},
		visible: {
			opacity: 1,
			transition: {
				staggerChildren: 0.2,
				delayChildren: 0.3,
			},
		},
	};

	const cardVariants = {
		hidden: {opacity: 0, y: 50},
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				type: 'spring',
				stiffness: 100,
				damping: 15,
			},
		},
	};

	const starVariants = {
		hidden: {scale: 0, rotate: -180},
		visible: (i: number) => ({
			scale: 1,
			rotate: 0,
			transition: {
				type: 'spring',
				stiffness: 200,
				delay: i * 0.1,
			},
		}),
	};
	return (
		<section className='py-20 bg-white relative overflow-hidden' ref={ref}>
			<motion.div
				className='absolute inset-0 bg-gradient-to-br from-red-50/30 via-transparent to-transparent'
				initial={{opacity: 0}}
				animate={isInView ? {opacity: 1} : {opacity: 0}}
				transition={{duration: 1}}
			/>
			<motion.div className='container mx-auto px-4' variants={containerVariants} initial='hidden' animate={isInView ? 'visible' : 'hidden'}>
				<motion.div
					className='text-center mb-16'
					initial={{opacity: 0, y: 20}}
					animate={isInView ? {opacity: 1, y: 0} : {opacity: 0, y: 20}}
					transition={{duration: 0.6}}>
					<motion.span
						className='inline-block px-4 py-1 mb-4 text-sm font-medium rounded-full bg-red-100 text-red-800'
						whileHover={{scale: 1.05}}
						whileTap={{scale: 0.95}}>
						Success Stories
					</motion.span>
					<motion.h2
						className='text-3xl md:text-4xl font-bold mb-4 relative inline-block'
						initial={{opacity: 0, y: 20}}
						animate={isInView ? {opacity: 1, y: 0} : {opacity: 0, y: 20}}
						transition={{duration: 0.6, delay: 0.2}}>
						What Our IMGs Say
						<motion.span
							className='absolute -bottom-2 left-0 w-full h-1 bg-red-200'
							initial={{scaleX: 0}}
							animate={isInView ? {scaleX: 1} : {scaleX: 0}}
							transition={{delay: 0.5, duration: 0.6}}
						/>
					</motion.h2>
					<motion.p
						className='text-gray-600 max-w-2xl mx-auto'
						initial={{opacity: 0}}
						animate={isInView ? {opacity: 1} : {opacity: 0}}
						transition={{delay: 0.3}}>
						Hear from international medical graduates who have successfully navigated their journey with our support
					</motion.p>
				</motion.div>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
					{testimonials.map((testimonial, index) => (
						<motion.div
							key={testimonial.name}
							variants={cardVariants}
							whileHover={{
								y: -10,
								transition: {duration: 0.2},
							}}
							className='bg-white rounded-xl p-8 shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-100 relative group'>
							<Quote className='absolute -top-4 -left-2 w-8 h-8 text-red-100 transform -rotate-12' />
							<motion.div className='flex mb-4'>
								{[...Array(testimonial.rating)].map((_, i) => (
									<motion.div key={i} custom={i} variants={starVariants}>
										<Star className='w-5 h-5 text-yellow-400 fill-current' />
									</motion.div>
								))}
							</motion.div>
							<motion.div className='relative' whileHover={{scale: 1.02}} transition={{duration: 0.2}}>
								<p className='text-gray-700 mb-6 italic relative z-10'>"{testimonial.content}"</p>
								<motion.div
									className='absolute inset-0 bg-gradient-to-br from-red-50/30 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-lg'
									initial={false}
									animate={{opacity: 0}}
									whileHover={{opacity: 1}}
								/>
							</motion.div>
							<motion.div className='flex items-center' initial={{opacity: 0}} animate={{opacity: 1}} transition={{delay: 0.5 + index * 0.1}}>
								<div className='flex-1'>
									<motion.h4 className='font-semibold text-gray-900' whileHover={{x: 5}} transition={{duration: 0.2}}>
										{testimonial.name}
									</motion.h4>
									<p className='text-sm text-gray-600'>{testimonial.role}</p>
								</div>
							</motion.div>
						</motion.div>
					))}
				</div>
				<motion.div
					className='text-center mt-12'
					initial={{opacity: 0, y: 20}}
					animate={isInView ? {opacity: 1, y: 0} : {opacity: 0, y: 20}}
					transition={{delay: 0.8}}>
					<motion.div whileHover={{scale: 1.05}} whileTap={{scale: 0.95}}>
						<motion.button className='inline-flex items-center px-6 py-3 bg-red-700 hover:bg-red-800 text-white font-medium rounded-md relative overflow-hidden group'>
							<motion.span
								className='absolute inset-0 bg-white opacity-0 group-hover:opacity-10'
								initial={false}
								animate={{scale: [1, 2], opacity: [0, 0.1, 0]}}
								transition={{duration: 0.5, repeat: Infinity}}
							/>
							<span className='relative z-10'>View All Success Stories</span>
							<motion.svg
								className='ml-2 w-5 h-5 relative z-10'
								viewBox='0 0 24 24'
								fill='none'
								xmlns='http://www.w3.org/2000/svg'
								animate={{x: [0, 5, 0]}}
								transition={{duration: 1.5, repeat: Infinity, ease: 'easeInOut'}}>
								<path d='M5 12H19M19 12L12 5M19 12L12 19' stroke='currentColor' strokeWidth='2' strokeLinecap='round' strokeLinejoin='round' />
							</motion.svg>
						</motion.button>
					</motion.div>
				</motion.div>
			</motion.div>
		</section>
	);
};
