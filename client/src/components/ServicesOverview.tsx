import {motion} from 'framer-motion';
import {GraduationCap, Users, Video, FileQuestion, ClipboardList, MessageSquare, Calendar, BookOpen} from 'lucide-react';
import {Button} from './ui/button';
import {useNavigate} from 'react-router-dom';

const services = [
	{
		icon: FileQuestion,
		title: 'Mock Exams',
		description: 'Comprehensive mock exams for MCCQE, NAC OSCE, MMI, MCCQE & NAC-OSCE, FMProC',
		price: 'From $199',
		route: '/mock-exams',
	},
	{
		icon: ClipboardList,
		title: 'CaRMS Application Support',
		description: 'Expert guidance for Canadian residency applications',
		price: 'From $899',
		route: '/carms-support',
	},
	{
		icon: Users,
		title: 'Paid Tutoring',
		description: 'Individual/group tutoring for NAC OSCE, MCCQE, USMLE Step 1 & 2',
		price: 'From $99/hour',
		route: '/tutoring',
	},
	{
		icon: MessageSquare,
		title: 'Consultations',
		description: 'One-on-one consultations for personalized guidance',
		price: 'From $149/session',
		route: '/consultations',
	},
	{
		icon: Video,
		title: 'Online Courses',
		description: 'Comprehensive exam preparation courses with exclusive content',
		price: 'From $499',
		route: '/courses',
	},
	{
		icon: BookOpen,
		title: 'Question Bank Access',
		description: 'Extensive question banks for all major medical exams',
		price: 'From $99/month',
		route: '/question-bank',
	},
	{
		icon: Calendar,
		title: 'Interview Preparation',
		description: 'Mock interviews and MMI practice sessions',
		price: 'From $199/session',
		route: '/interview-prep',
	},
	{
		icon: GraduationCap,
		title: 'Study Group Partner Finder',
		description: 'Find your ideal study partner based on your preferences',
		price: 'From $29/month',
		route: '/study-partner',
	},
];

export const ServicesOverview = () => {
	const navigate = useNavigate();

	return (
		<section className='py-20'>
			<div className='relative mb-20'>
				<img src='/services_image.jpeg' alt='Medical equipment' className='w-full h-64 object-cover' />
				<div className='absolute inset-0 bg-red-900 bg-opacity-70 flex items-center justify-center'>
					<div className='text-center text-white'>
						<h2 className='text-4xl font-bold mb-4'>Our Services</h2>
						<p className='text-xl max-w-2xl mx-auto'>Comprehensive support for international medical graduates pursuing their career goals</p>
					</div>
				</div>
			</div>
			<div className='container mx-auto px-4'>
				<div className='grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8'>
					{services.map((service, index) => (
						<motion.div
							key={service.title}
							initial={{opacity: 0, y: 20}}
							animate={{opacity: 1, y: 0}}
							transition={{delay: index * 0.1}}
							className='bg-white rounded-xl shadow-lg p-6 hover:shadow-xl transition-shadow'>
							<service.icon className='h-12 w-12 text-red-600 mb-4' />
							<h3 className='text-xl font-semibold mb-2'>{service.title}</h3>
							<p className='text-gray-600 mb-4'>{service.description}</p>
							<div className='flex justify-between items-center'>
								<span className='text-red-600 font-semibold'>{service.price}</span>
								<Button variant='outline' onClick={() => navigate(service.route)} className='border-red-600 text-red-600 hover:bg-red-50'>
									Learn More
								</Button>
							</div>
						</motion.div>
					))}
				</div>
			</div>
		</section>
	);
};
