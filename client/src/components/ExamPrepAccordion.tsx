import React from 'react';
import {Button} from './ui/button';
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from './ui/accordion';
import {ExternalLink} from 'lucide-react';

interface ContentItem {
	title: string;
	description: string;
	link?: string;
	linkText?: string;
	buttonText?: string;
}

interface ExamPrepSection {
	title: string;
	content: ContentItem[];
}

const ExamPrepAccordion = () => {
	const examPrepContent: ExamPrepSection[] = [
		{
			title: 'MCCQE',
			content: [
				{
					title: 'Overview',
					description:
						'The Medical Council of Canada Qualifying Examination (MCCQE) Part I is a one-day, computer-based test that assesses the competence of candidates who have obtained their medical degree for entry into postgraduate training programs.',
				},
				{
					title: 'Exam Format',
					description:
						'The examination consists of up to 210 multiple-choice questions and short-answer questions, focusing on clinical decision-making and critical thinking.',
				},
				{
					title: 'Preparation Tips',
					description:
						'Focus on high-yield topics, practice with sample questions, and develop a structured study schedule. Our comprehensive study materials and practice tests can help you prepare effectively.',
				},
			],
		},
		{
			title: 'MCCQE QBank Books',
			content: [
				{
					title: 'Recommended Books',
					description:
						'Access our curated selection of high-quality MCCQE preparation books on Amazon. These resources have been carefully selected based on their effectiveness and positive student feedback.',
					link: 'https://amazon.com/MCCQE-books',
					linkText: 'View Books on Amazon',
				},
			],
		},
		{
			title: 'MCCQE PrepBot',
			content: [
				{
					title: 'AI-Powered Learning',
					description: 'Our PrepBot uses advanced AI to create personalized study plans and practice questions tailored to your needs.',
				},
				{
					title: 'Features',
					description: 'Includes adaptive questioning, performance analytics, and targeted feedback to help you improve efficiently.',
				},
				{
					title: 'How to Use',
					description:
						'Start with an assessment test, then follow the personalized study plan. Regular practice with the PrepBot helps identify and strengthen weak areas.',
				},
			],
		},
		{
			title: 'Practice Tests',
			content: [
				{
					title: 'Free Practice Test',
					description: 'Try our free MCCQE practice test to get a feel for the exam format and assess your current knowledge level.',
					buttonText: 'Start Free Test',
				},
				{
					title: 'Full Practice Tests',
					description: 'Access our complete set of MCCQE Part 1 practice tests, designed to simulate the actual exam experience.',
					buttonText: 'View All Tests',
				},
			],
		},
	];

	return (
		<div className='max-w-4xl mx-auto p-6'>
			<h2 className='text-3xl font-bold text-center mb-8'>Exam Preparation Resources</h2>
			<Accordion type='single' collapsible className='w-full'>
				{examPrepContent.map((section, index) => (
					<AccordionItem key={index} value={`item-${index}`}>
						<AccordionTrigger className='text-xl font-semibold'>{section.title}</AccordionTrigger>
						<AccordionContent>
							<div className='space-y-6'>
								{section.content.map((item, itemIndex) => (
									<div key={itemIndex} className='space-y-2'>
										<h3 className='font-semibold text-lg'>{item.title}</h3>
										<p className='text-gray-600'>{item.description}</p>
										{item.link && (
											<a
												href={item.link}
												target='_blank'
												rel='noopener noreferrer'
												className='inline-flex items-center text-red-600 hover:text-red-700 mt-2'>
												{item.linkText} <ExternalLink className='ml-1 h-4 w-4' />
											</a>
										)}
										{item.buttonText && (
											<Button variant='outline' className='mt-2' onClick={() => console.log(`Clicked: ${item.buttonText}`)}>
												{item.buttonText}
											</Button>
										)}
									</div>
								))}
							</div>
						</AccordionContent>
					</AccordionItem>
				))}
			</Accordion>
		</div>
	);
};

export default ExamPrepAccordion;
