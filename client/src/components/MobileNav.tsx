import {<PERSON>u, <PERSON>, ChevronDown} from 'lucide-react';
import {motion, AnimatePresence} from 'framer-motion';
import {Button} from './ui/button';
import {Sheet, Sheet<PERSON>ontent, She<PERSON><PERSON>eader, Sheet<PERSON><PERSON>le, SheetTrigger} from './ui/sheet';
import {Accordion, AccordionContent, AccordionItem, AccordionTrigger} from './ui/accordion';
import {Link} from 'react-router-dom';

const examPrepLinks = [
	{
		title: 'MCCQE',
		href: '/courses',
		description: 'Medical Council of Canada Qualifying Examination',
	},
	{
		title: 'NAC-OSCE',
		href: '/courses',
		description: 'National Assessment Collaboration Examination',
	},
	{
		title: 'CASPER® Prep',
		href: '/casper-prep',
		description: 'Computer-Based Assessment for Sampling Personal Characteristics',
	},
	{
		title: 'Practice Tests',
		href: '/mock-exams',
		description: 'Full-length Practice Tests',
	},
];

const coachingLinks = [
	{
		title: 'One-on-One Coaching',
		href: '/coaching/individual',
		description: 'Personalized Sessions',
	},
	{
		title: 'Group Sessions',
		href: '/coaching/group',
		description: 'Interactive Learning',
	},
	{
		title: 'Mock Interviews',
		href: '/coaching/interviews',
		description: 'Interview Practice',
	},
	{
		title: 'Application Review',
		href: '/coaching/application',
		description: 'Expert Review Service',
	},
];

export const MobileNav = () => {
	const containerVariants = {
		hidden: {opacity: 0, x: -20},
		visible: {
			opacity: 1,
			x: 0,
			transition: {
				staggerChildren: 0.1,
				delayChildren: 0.3,
			},
		},
		exit: {opacity: 0, x: 20},
	};

	const itemVariants = {
		hidden: {opacity: 0, x: -20},
		visible: {
			opacity: 1,
			x: 0,
			transition: {duration: 0.3},
		},
	};

	return (
		<Sheet>
			<SheetTrigger asChild>
				<Button variant='ghost' className='md:hidden' size='icon'>
					<Menu className='h-6 w-6' />
					<span className='sr-only'>Toggle menu</span>
				</Button>
			</SheetTrigger>
			<SheetContent side='left' className='w-[300px] sm:w-[400px] p-0'>
				<motion.div className='h-full bg-white p-6 overflow-y-auto' variants={containerVariants} initial='hidden' animate='visible' exit='exit'>
					<SheetHeader className='mb-8'>
						<SheetTitle>
							<Link to='/' className='text-red-700 text-2xl font-bold flex items-center group'>
								<motion.img src='/Universal MD.png' alt='Logo' className='w-10 h-10 mr-2' whileHover={{rotate: 360}} transition={{duration: 0.5}} />
								<span className='group-hover:text-red-800 transition-colors'>UniversalMD</span>
							</Link>
						</SheetTitle>
					</SheetHeader>

					<motion.nav className='flex flex-col gap-2' variants={containerVariants}>
						<Link
							to='/about'
							className='flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:text-red-700 hover:bg-red-50 transition-all duration-200'>
							ABOUT
						</Link>

						<Accordion type='single' collapsible className='w-full'>
							<AccordionItem value='coaching'>
								<AccordionTrigger className='p-3 hover:text-red-700 hover:bg-red-50 rounded-lg'>COACHING</AccordionTrigger>
								<AccordionContent>
									<div className='flex flex-col gap-2 pl-4'>
										{coachingLinks.map((item) => (
											<Link key={item.href} to={item.href} className='p-2 text-gray-700 hover:text-red-700 rounded-md'>
												<div className='font-medium'>{item.title}</div>
												<div className='text-sm text-gray-500'>{item.description}</div>
											</Link>
										))}
									</div>
								</AccordionContent>
							</AccordionItem>

							<AccordionItem value='exam-prep'>
								<AccordionTrigger className='p-3 hover:text-red-700 hover:bg-red-50 rounded-lg'>EXAM PREP</AccordionTrigger>
								<AccordionContent>
									<div className='flex flex-col gap-2 pl-4'>
										{examPrepLinks.map((item) => (
											<Link key={item.href} to={item.href} className='p-2 text-gray-700 hover:text-red-700 rounded-md'>
												<div className='font-medium'>{item.title}</div>
												<div className='text-sm text-gray-500'>{item.description}</div>
											</Link>
										))}
									</div>
								</AccordionContent>
							</AccordionItem>
						</Accordion>

						<Link
							to='/study-partner'
							className='flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:text-red-700 hover:bg-red-50 transition-all duration-200'>
							FIND STUDY PARTNER
						</Link>

						<Link
							to='/interview-prep'
							className='flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:text-red-700 hover:bg-red-50 transition-all duration-200'>
							INTERVIEW PREP
						</Link>

						<Link
							to='/courses'
							className='flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:text-red-700 hover:bg-red-50 transition-all duration-200'>
							COURSES
						</Link>

						<Link
							to='/packages'
							className='flex items-center gap-3 p-3 rounded-lg text-gray-700 hover:text-red-700 hover:bg-red-50 transition-all duration-200'>
							PACKAGES
						</Link>
					</motion.nav>

					<motion.div className='flex flex-col gap-4 mt-8 pt-6 border-t' variants={containerVariants}>
						<motion.div variants={itemVariants}>
							<Button className='w-full bg-red-700 hover:bg-red-800 text-white' onClick={() => console.log('Book a Session clicked')}>
								BOOK A SESSION
							</Button>
						</motion.div>
					</motion.div>
				</motion.div>
			</SheetContent>
		</Sheet>
	);
};
