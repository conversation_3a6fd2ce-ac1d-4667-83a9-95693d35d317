import {motion, useInView, useScroll, useTransform} from 'framer-motion';
import {Target, Heart, Lightbulb, ArrowRight} from 'lucide-react';
import {useRef} from 'react';

export const Mission = () => {
	const ref = useRef(null);
	const isInView = useInView(ref, {once: true, margin: '-100px'});
	const {scrollYProgress} = useScroll({
		target: ref,
		offset: ['start end', 'end start'],
	});

	const y = useTransform(scrollYProgress, [0, 1], [100, -100]);
	const opacity = useTransform(scrollYProgress, [0, 0.5, 1], [0, 1, 0]);

	const containerVariants = {
		hidden: {opacity: 0},
		visible: {
			opacity: 1,
			transition: {
				staggerChildren: 0.2,
				delayChildren: 0.3,
			},
		},
	};

	const cardVariants = {
		hidden: {opacity: 0, scale: 0.8},
		visible: {
			opacity: 1,
			scale: 1,
			transition: {
				type: 'spring',
				stiffness: 100,
				damping: 15,
			},
		},
	};

	const iconVariants = {
		hidden: {scale: 0, rotate: -180},
		visible: {
			scale: 1,
			rotate: 0,
			transition: {
				type: 'spring',
				stiffness: 200,
				damping: 20,
			},
		},
	};

	return (
		<section className='py-20 relative overflow-hidden' ref={ref}>
			<motion.div className='absolute inset-0 bg-gradient-to-br from-red-50/50 to-transparent' style={{opacity, y}} />
			<motion.div className='container mx-auto px-4 relative' variants={containerVariants} initial='hidden' animate={isInView ? 'visible' : 'hidden'}>
				<motion.div
					className='text-center mb-16'
					initial={{opacity: 0, y: 20}}
					animate={isInView ? {opacity: 1, y: 0} : {opacity: 0, y: 20}}
					transition={{duration: 0.6}}>
					<motion.h2 className='text-3xl md:text-4xl font-bold mb-4 relative inline-block'>
						Our Mission & Values
						<motion.span
							className='absolute -bottom-2 left-0 w-full h-1 bg-red-200'
							initial={{scaleX: 0}}
							animate={isInView ? {scaleX: 1} : {scaleX: 0}}
							transition={{delay: 0.5, duration: 0.6}}
						/>
					</motion.h2>
					<motion.p
						className='text-gray-600 max-w-2xl mx-auto'
						initial={{opacity: 0}}
						animate={isInView ? {opacity: 1} : {opacity: 0}}
						transition={{delay: 0.3}}>
						Supporting healthcare professionals worldwide in achieving their dreams
					</motion.p>
				</motion.div>

				<motion.div className='grid grid-cols-1 md:grid-cols-3 gap-8'>
					<motion.div
						variants={cardVariants}
						whileHover={{
							y: -10,
							transition: {duration: 0.2},
						}}
						className='glass-card rounded-xl p-8 text-center relative group'>
						<motion.div variants={iconVariants} className='relative z-10'>
							<Target className='h-12 w-12 text-red-600 mx-auto mb-6' />
						</motion.div>
						<motion.div
							className='absolute inset-0 bg-gradient-to-br from-red-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl'
							initial={false}
							animate={{opacity: 0}}
							whileHover={{opacity: 1}}
						/>
						<h3 className='text-xl font-semibold mb-4 relative z-10'>Vision</h3>
						<p className='text-gray-600 relative z-10'>Support all healthcare professionals around the world in reaching their dreams</p>
						<motion.div
							className='absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity'
							initial={{scale: 0}}
							whileHover={{scale: 1.1}}>
							<ArrowRight className='w-5 h-5 text-red-600' />
						</motion.div>
					</motion.div>

					<motion.div
						variants={cardVariants}
						whileHover={{
							y: -10,
							transition: {duration: 0.2},
						}}
						className='glass-card rounded-xl p-8 text-center relative group'>
						<motion.div variants={iconVariants} className='relative z-10'>
							<Heart className='h-12 w-12 text-red-600 mx-auto mb-6' />
						</motion.div>
						<motion.div
							className='absolute inset-0 bg-gradient-to-br from-red-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl'
							initial={false}
							animate={{opacity: 0}}
							whileHover={{opacity: 1}}
						/>
						<h3 className='text-xl font-semibold mb-4 relative z-10'>Mission</h3>
						<p className='text-gray-600 relative z-10'>Simplify processes needed for healthcare professionals to meet practice requirements worldwide</p>
						<motion.div
							className='absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity'
							initial={{scale: 0}}
							whileHover={{scale: 1.1}}>
							<ArrowRight className='w-5 h-5 text-red-600' />
						</motion.div>
					</motion.div>

					<motion.div
						variants={cardVariants}
						whileHover={{
							y: -10,
							transition: {duration: 0.2},
						}}
						className='glass-card rounded-xl p-8 text-center relative group'>
						<motion.div variants={iconVariants} className='relative z-10'>
							<Lightbulb className='h-12 w-12 text-red-600 mx-auto mb-6' />
						</motion.div>
						<motion.div
							className='absolute inset-0 bg-gradient-to-br from-red-50 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300 rounded-xl'
							initial={false}
							animate={{opacity: 0}}
							whileHover={{opacity: 1}}
						/>
						<h3 className='text-xl font-semibold mb-4 relative z-10'>Values</h3>
						<p className='text-gray-600 relative z-10'>Healthcare innovation, diversity, support with more impact with less resources</p>
						<motion.div
							className='absolute bottom-4 right-4 opacity-0 group-hover:opacity-100 transition-opacity'
							initial={{scale: 0}}
							whileHover={{scale: 1.1}}>
							<ArrowRight className='w-5 h-5 text-red-600' />
						</motion.div>
					</motion.div>
				</motion.div>
			</motion.div>
		</section>
	);
};
