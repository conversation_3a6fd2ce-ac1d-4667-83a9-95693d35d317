import { useEffect, useState } from 'react';
import { StudyPartnerProfile } from '../types';

export const StudyPartnerList = () => {
  const [studyPartners, setStudyPartners] = useState<StudyPartnerProfile[]>([]);

  useEffect(() => {
    const fetchStudyPartners = async () => {
      try {
        const response = await fetch('/api/study-partners');
        if (!response.ok) {
          throw new Error('Failed to fetch study partners');
        }
        const data = await response.json();
        setStudyPartners(data);
      } catch (error) {
        console.error('Error fetching study partners:', error);
      }
    };

    fetchStudyPartners();
  }, []);

  return (
    <div className="max-w-4xl mx-auto mt-8">
      <h2 className="text-2xl font-bold mb-4">Available Study Partners</h2>
      {studyPartners.length === 0 ? (
        <p>No study partners available at the moment.</p>
      ) : (
        <ul className="space-y-4">
          {studyPartners.map((partner) => (
            <li key={partner.id} className="border p-4 rounded-lg">
              <h3 className="text-xl font-semibold">{partner.specialty} Student</h3>
              <p><strong>Study Times:</strong> {partner.studyTimes}</p>
              <p><strong>Topics of Interest:</strong> {partner.topicsOfInterest}</p>
              <p><strong>Exam Timeline:</strong> {partner.examDate}</p>
              <p><strong>Location:</strong> {partner.location}</p>
            </li>
          ))}
        </ul>
      )}
    </div>
  );
};
