import {useState, useEffect} from 'react';
import {motion, AnimatePresence} from 'framer-motion';
import {But<PERSON>} from './ui/button';
import {Select, SelectContent, SelectItem, SelectTrigger, SelectValue} from './ui/select';
import {Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage} from './ui/form';
import {Input} from './ui/input';
import {Progress} from './ui/progress';
import {zodResolver} from '@hookform/resolvers/zod';
import {useForm} from 'react-hook-form';
import {Loader2, CheckCircle2, AlertCircle} from 'lucide-react';
import {Alert, AlertDescription} from './ui/alert';
import * as z from 'zod';

const studyTimes = ['Weekdays', 'Weekends', 'Evenings', 'Flexible'];
const examTypes = ['MCCQE', 'USMLE Step 1', 'NAC OSCE', 'MCCQE & NAC-OSCE', 'Medical Ethics'];
const studyMethods = ['Active Recall', 'Discussion-based', 'Video-based', 'Question Banks'];
const studyMaterials = ['Toronto Notes', 'First Aid', 'UWorld', 'Boards and Beyond'];
const languages = ['English', 'French', 'Bilingual'];
const specialties = ['Internal Medicine', 'Surgery', 'Pediatrics', 'Family Medicine'];
const environments = ['Quiet', 'Structured Schedule', 'Frequent Breaks', 'Flexible'];
const paces = ['Slow-paced', 'Moderate', 'Fast-paced', 'Intensive'];
const formats = ['Online Videos', 'Books', 'Practice Exams', 'Interactive Quizzes'];

const formSchema = z.object({
	studyTimes: z.string().min(1, 'Please select your preferred study times'),
	availability: z.string().min(1, 'Please specify your availability'),
	topicsOfInterest: z.string().min(1, 'Please select topics of interest'),
	preferredMaterials: z.string().min(1, 'Please select preferred study materials'),
	studyMethod: z.string().min(1, 'Please select your preferred study method'),
	examDate: z.string().min(1, 'Please specify your target exam date'),
	location: z.string().optional(),
	timeZone: z.string().min(1, 'Please specify your time zone'),
	studyGoals: z.string().min(1, 'Please specify your study goals'),
	language: z.string().min(1, 'Please select your preferred language'),
	specialty: z.string().min(1, 'Please select your medical specialty'),
	environment: z.string().min(1, 'Please select your preferred study environment'),
	pace: z.string().min(1, 'Please select your preferred study pace'),
	format: z.string().min(1, 'Please select your preferred learning format'),
	longTermGoals: z.string().min(1, 'Please specify your long-term goals'),
});

export const StudyPartnerMatcher = () => {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [formProgress, setFormProgress] = useState(0);
	const [showSuccess, setShowSuccess] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [completedFields, setCompletedFields] = useState<Set<string>>(new Set());

	const form = useForm<z.infer<typeof formSchema>>({
		resolver: zodResolver(formSchema),
	});

	useEffect(() => {
		const totalFields = Object.keys(formSchema.shape).length;
		const progress = (completedFields.size / totalFields) * 100;
		setFormProgress(progress);
	}, [completedFields]);

	const updateCompletedFields = (fieldName: string, value: any) => {
		const newCompletedFields = new Set(completedFields);
		if (value && value.length > 0) {
			newCompletedFields.add(fieldName);
		} else {
			newCompletedFields.delete(fieldName);
		}
		setCompletedFields(newCompletedFields);
	};

	const onSubmit = async (values: z.infer<typeof formSchema>) => {
		try {
			setIsSubmitting(true);
			setError(null);
			// API call for partner matching
			const response = await fetch('/api/study-partner/search', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
				},
				body: JSON.stringify({
					...values,
					// Remove groupSizePreference since it's not defined in the form schema
					// Remove genderPreference since it's not defined in the form schema
				}),
			});

			if (!response.ok) throw new Error('Match failed');
			const matches = await response.json();
			console.log('Found matches:', matches);
			setShowSuccess(true);
			setTimeout(() => setShowSuccess(false), 3000);
		} catch (err) {
			setError(err instanceof Error ? err.message : 'Failed to submit form');
		} finally {
			setIsSubmitting(false);
		}
	};

	const formContainerVariants = {
		hidden: {opacity: 0, y: 20},
		visible: {
			opacity: 1,
			y: 0,
			transition: {
				duration: 0.6,
				staggerChildren: 0.1,
			},
		},
	};

	const formItemVariants = {
		hidden: {opacity: 0, y: 10},
		visible: {
			opacity: 1,
			y: 0,
			transition: {duration: 0.4},
		},
	};

	return (
		<section className='py-20 bg-gradient-to-b from-white to-gray-50'>
			<div className='container mx-auto px-4'>
				<motion.div className='text-center mb-16' initial={{opacity: 0, y: 20}} animate={{opacity: 1, y: 0}} transition={{duration: 0.6}}>
					<motion.span
						className='inline-block px-4 py-1 mb-4 text-sm font-medium rounded-full bg-red-100 text-red-800'
						whileHover={{scale: 1.05}}
						transition={{duration: 0.2}}>
						Study Partner Matching
					</motion.span>
					<motion.h2
						className='text-3xl md:text-4xl font-bold mb-4'
						variants={{
							hidden: {opacity: 0, y: 20},
							visible: {opacity: 1, y: 0},
						}}
						initial='hidden'
						animate='visible'
						transition={{duration: 0.6, delay: 0.2}}>
						Find Your Perfect Study Partner
					</motion.h2>
					<p className='text-gray-600 max-w-2xl mx-auto'>
						Connect with other IMGs who share your study goals, schedule, and learning style. Our matching system helps you find the ideal study
						partner for your medical journey.
					</p>
					<div className='mt-6'>
						<Progress value={formProgress} className='w-full max-w-md mx-auto' />
						<p className='text-sm text-gray-600 mt-2'>Profile Completion: {Math.round(formProgress)}%</p>
					</div>
				</motion.div>

				<motion.div
					variants={formContainerVariants}
					initial='hidden'
					animate='visible'
					className='max-w-3xl mx-auto bg-white rounded-2xl p-8 shadow-lg border border-gray-100'>
					<Form {...form}>
						<form onSubmit={form.handleSubmit(onSubmit)} className='space-y-6'>
							<motion.div variants={formItemVariants} className='grid grid-cols-1 md:grid-cols-2 gap-6'>
								<FormField
									control={form.control}
									name='studyTimes'
									render={({field}) => (
										<motion.div variants={formItemVariants}>
											<FormItem>
												<FormLabel className='flex items-center gap-2'>
													Preferred Study Times
													{completedFields.has('studyTimes') && <CheckCircle2 className='h-4 w-4 text-green-500' />}
												</FormLabel>
												<Select
													onValueChange={(value) => {
														field.onChange(value);
														updateCompletedFields('studyTimes', value);
													}}
													defaultValue={field.value}>
													<FormControl>
														<SelectTrigger>
															<SelectValue placeholder='Select study times' />
														</SelectTrigger>
													</FormControl>
													<SelectContent>
														{studyTimes.map((time) => (
															<SelectItem key={time} value={time}>
																{time}
															</SelectItem>
														))}
													</SelectContent>
												</Select>
												<FormMessage />
											</FormItem>
										</motion.div>
									)}
								/>

								<FormField
									control={form.control}
									name='availability'
									render={({field}) => (
										<motion.div variants={formItemVariants}>
											<FormItem>
												<FormLabel className='flex items-center gap-2'>
													Weekly Availability
													{completedFields.has('availability') && <CheckCircle2 className='h-4 w-4 text-green-500' />}
												</FormLabel>
												<FormControl>
													<Input
														placeholder='e.g., 2 hours/day, weekends only'
														{...field}
														onChange={(e) => {
															field.onChange(e);
															updateCompletedFields('availability', e.target.value);
														}}
													/>
												</FormControl>
												<FormMessage />
											</FormItem>
										</motion.div>
									)}
								/>
							</motion.div>

							{/* Similar pattern for other form fields... */}

							<AnimatePresence mode='wait'>
								{error && (
									<motion.div initial={{opacity: 0, height: 0}} animate={{opacity: 1, height: 'auto'}} exit={{opacity: 0, height: 0}}>
										<Alert variant='destructive' className='mb-4'>
											<AlertCircle className='h-4 w-4' />
											<AlertDescription>{error}</AlertDescription>
										</Alert>
									</motion.div>
								)}
							</AnimatePresence>

							<motion.div variants={formItemVariants} whileHover={!isSubmitting && formProgress >= 60 ? {scale: 1.01} : {}}>
								<Button type='submit' className='w-full bg-red-700 hover:bg-red-800 text-white relative' disabled={isSubmitting || formProgress < 60}>
									{isSubmitting ? (
										<>
											<Loader2 className='mr-2 h-4 w-4 animate-spin' />
											Finding Matches...
										</>
									) : showSuccess ? (
										<>
											<CheckCircle2 className='mr-2 h-4 w-4' />
											Match Request Submitted!
										</>
									) : (
										'Find Study Partners'
									)}
								</Button>

								{formProgress < 60 && (
									<p className='text-sm text-red-600 mt-2 text-center'>Please complete at least 60% of your profile to find matches</p>
								)}
							</motion.div>
						</form>
					</Form>
				</motion.div>

				<motion.div className='mt-12 text-center text-gray-600' initial={{opacity: 0}} animate={{opacity: 1}} transition={{delay: 0.8}}>
					<p className='mb-4'>Our matching algorithm ensures a minimum 60% compatibility between study partners.</p>
					<p>You'll receive an email notification when we find suitable matches for you.</p>
				</motion.div>
			</div>
		</section>
	);
};
