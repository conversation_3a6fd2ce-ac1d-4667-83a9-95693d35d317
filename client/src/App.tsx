import React, {useState, useEffect} from 'react';
import {Toaster} from './components/ui/toaster';
import {Toaster as Sonner} from './components/ui/sonner';
import {TooltipProvider} from './components/ui/tooltip';
import {motion, AnimatePresence} from 'framer-motion';
import {Button} from './components/ui/button';
import {MobileNav} from './components/MobileNav';
import TopBar from './components/TopBar';
import MainNav from './components/MainNav';
import {QueryClient, QueryClientProvider} from '@tanstack/react-query';
import {BrowserRouter, Routes, Route, Link} from 'react-router-dom';
import Index from './pages/Index';
import Services from './pages/Services';
import StudyPartner from './pages/StudyPartner';
import MockExams from './pages/MockExams';
import Blog from './pages/Blog';
import About from './pages/About';
import CasperPrep from './pages/CasperPrep';
import InterviewPrep from './pages/InterviewPrep';
import Courses from './pages/Courses';
import Packages from './pages/Packages';
import AdminAvailabilityPage from './pages/AdminAvailabilityPage'; // Import the new page

const queryClient = new QueryClient({
	defaultOptions: {
		queries: {
			refetchOnWindowFocus: false,
			retry: false,
		},
	},
});

const App: React.FC = () => {
	const [isLoading, setIsLoading] = useState(true);
	const [scrollProgress, setScrollProgress] = useState(0);

	useEffect(() => {
		// Simulate initial loading
		const timer = setTimeout(() => setIsLoading(false), 1000);

		// Handle scroll progress
		const handleScroll = () => {
			const totalScroll = document.documentElement.scrollHeight - window.innerHeight;
			const progress = (window.scrollY / totalScroll) * 100;
			setScrollProgress(progress);
		};

		window.addEventListener('scroll', handleScroll);
		return () => {
			clearTimeout(timer);
			window.removeEventListener('scroll', handleScroll);
		};
	}, []);

	if (isLoading) {
		return (
			<div className='fixed inset-0 bg-white dark:bg-slate-900 flex items-center justify-center'>
				<motion.div
					initial={{opacity: 0, scale: 0.8}}
					animate={{opacity: 1, scale: 1}}
					transition={{duration: 0.5, ease: [0.4, 0, 0.2, 1]}}
					className='text-center'>
					<motion.img
						src='/Universal MD.png'
						alt='Logo'
						className='w-20 h-20 mx-auto mb-6'
						initial={{rotate: 0}}
						animate={{rotate: 720}}
						transition={{
							duration: 2,
							ease: 'linear',
							repeat: Infinity,
						}}
					/>
					<motion.div
						className='h-1.5 w-32 bg-red-100 dark:bg-red-900 rounded-full overflow-hidden mx-auto'
						initial={{scale: 0.8, opacity: 0}}
						animate={{scale: 1, opacity: 1}}
						transition={{delay: 0.2, duration: 0.4}}>
						<motion.div
							className='h-full bg-red-700 dark:bg-red-600'
							initial={{x: '-100%'}}
							animate={{x: '100%'}}
							transition={{
								duration: 1.5,
								repeat: Infinity,
								ease: 'easeInOut',
							}}
						/>
					</motion.div>
				</motion.div>
			</div>
		);
	}

	return (
		<QueryClientProvider client={queryClient}>
			<TooltipProvider>
				<Toaster />
				<Sonner />
				<BrowserRouter>
					{/* Progress bar */}
					<motion.div className='fixed top-0 left-0 right-0 h-1 bg-red-700 origin-left z-50' style={{scaleX: scrollProgress / 100}} />

					<header className='fixed top-0 w-full z-40 bg-white/90 backdrop-blur-sm shadow-sm'>
						<TopBar />
						<motion.div className='container mx-auto px-4' initial={{opacity: 0, y: -20}} animate={{opacity: 1, y: 0}} transition={{duration: 0.5}}>
							<nav className='flex flex-wrap items-center justify-between py-4'>
								<div className='flex items-center justify-between w-full'>
									<motion.div className='flex items-center' whileHover={{scale: 1.02}} whileTap={{scale: 0.98}}>
										<MobileNav />
										<Link to='/' className='text-red-700 text-2xl font-bold flex items-center group ml-2'>
											<motion.img
												src='/Universal MD.png'
												alt='Logo'
												className='w-10 h-10 mr-2'
												whileHover={{rotate: 360}}
												transition={{duration: 0.5}}
											/>
											<span className='group-hover:text-red-800 transition-colors'>UniversalMD</span>
										</Link>
									</motion.div>
									<MainNav />
								</div>
							</nav>
						</motion.div>
					</header>
					<AnimatePresence mode='wait'>
						<motion.main
							key={location.pathname}
							className='min-h-screen bg-gray-50 pt-32'
							initial={{opacity: 0, y: 20}}
							animate={{opacity: 1, y: 0}}
							exit={{opacity: 0, y: -20}}
							transition={{duration: 0.3}}>
							<Routes>
								<Route path='/' element={<Index />} />
								<Route path='/about' element={<About />} />
								<Route path='/coaching/*' element={<Services />} />
								<Route path='/exam-prep/*' element={<Services />} />
								<Route path='/casper-prep' element={<CasperPrep />} />
								<Route path='/interview-prep' element={<InterviewPrep />} />
								<Route path='/courses' element={<Courses />} />
								<Route path='/packages' element={<Packages />} />
								<Route path='/study-partner' element={<StudyPartner />} />
								<Route path='/mock-exams' element={<MockExams />} />
								<Route path='/blog' element={<Blog />} />
								<Route path='/admin/availability' element={<AdminAvailabilityPage />} /> {/* Add the new route */}
							</Routes>
						</motion.main>
					</AnimatePresence>
					<motion.footer className='bg-gray-900 text-white py-12' initial={{opacity: 0}} animate={{opacity: 1}} transition={{delay: 0.4}}>
						<div className='container mx-auto px-4'>
							<div className='grid grid-cols-1 md:grid-cols-4 gap-8'>
								<div>
									<h3 className='text-xl font-bold mb-4'>universal MD</h3>
									<p className='text-gray-400'>Empowering International Medical Graduates on their journey to success.</p>
								</div>
								<div>
									<h4 className='text-lg font-semibold mb-4'>Quick Links</h4>
									<ul className='space-y-2'>
										<li>
											<Link to='/services' className='text-gray-400 hover:text-white'>
												Services
											</Link>
										</li>
										<li>
											<Link to='/study-partner' className='text-gray-400 hover:text-white'>
												Study Partner
											</Link>
										</li>
										<li>
											<Link to='/mock-exams' className='text-gray-400 hover:text-white'>
												Mock Exams
											</Link>
										</li>
										<li>
											<Link to='/blog' className='text-gray-400 hover:text-white'>
												Blog
											</Link>
										</li>
									</ul>
								</div>
								<div>
									<h4 className='text-lg font-semibold mb-4'>Resources</h4>
									<ul className='space-y-2'>
										<li>
											<Link to='/free-resources' className='text-gray-400 hover:text-white'>
												Free Resources
											</Link>
										</li>
										<li>
											<Link to='/testimonials' className='text-gray-400 hover:text-white'>
												Success Stories
											</Link>
										</li>
										<li>
											<Link to='/faq' className='text-gray-400 hover:text-white'>
												FAQ
											</Link>
										</li>
										<li>
											<Link to='/contact' className='text-gray-400 hover:text-white'>
												Contact Us
											</Link>
										</li>
									</ul>
								</div>
								<div>
									<h4 className='text-lg font-semibold mb-4'>Connect With Us</h4>
									<div className='flex space-x-4'>
										<a href='#' className='text-gray-400 hover:text-white'>
											<svg className='w-6 h-6' fill='currentColor' viewBox='0 0 24 24'>
												<path d='M22 12c0-5.523-4.477-10-10-10S2 6.477 2 12c0 4.991 3.657 9.128 8.438 9.878v-6.987h-2.54V12h2.54V9.797c0-2.506 1.492-3.89 3.777-3.89 1.094 0 2.238.195 2.238.195v2.46h-1.26c-1.243 0-1.63.771-1.63 1.562V12h2.773l-.443 2.89h-2.33v6.988C18.343 21.128 22 16.991 22 12z' />
											</svg>
										</a>
										<a href='#' className='text-gray-400 hover:text-white'>
											<svg className='w-6 h-6' fill='currentColor' viewBox='0 0 24 24'>
												<path d='M23 3a10.9 10.9 0 0 1-3.14 1.53 4.48 4.48 0 0 0-7.86 3v1A10.66 10.66 0 0 1 3 4s-4 9 5 13a11.64 11.64 0 0 1-7 2c9 5 20 0 20-11.5a4.5 4.5 0 0 0-.08-.83A7.72 7.72 0 0 0 23 3z' />
											</svg>
										</a>
										<a href='#' className='text-gray-400 hover:text-white'>
											<svg className='w-6 h-6' fill='currentColor' viewBox='0 0 24 24'>
												<path d='M16 8a6 6 0 0 1 6 6v7h-4v-7a2 2 0 0 0-2-2 2 2 0 0 0-2 2v7h-4v-7a6 6 0 0 1 6-6z M2 9h4v12H2z M4 2a2 2 0 1 1 0 4 2 2 0 0 1 0-4z' />
											</svg>
										</a>
									</div>
									<div className='mt-4'>
										<h5 className='font-semibold mb-2'>Newsletter</h5>
										<div className='flex'>
											<input
												type='email'
												placeholder='Enter your email'
												className='px-3 py-2 bg-gray-800 text-white rounded-l-md focus:outline-none focus:ring-1 focus:ring-red-500'
											/>
											<button className='px-4 py-2 bg-red-700 text-white rounded-r-md hover:bg-red-800'>Subscribe</button>
										</div>
									</div>
								</div>
							</div>
							<motion.div
								className='border-t border-gray-800 mt-8 pt-8 text-center text-gray-400'
								initial={{opacity: 0}}
								animate={{opacity: 1}}
								transition={{delay: 0.6}}>
								<p>© {new Date().getFullYear()} UniversalMD. All rights reserved.</p>
								<p className='mt-2 text-sm'>
									Made with <span className='text-red-500'>♥</span> for International Medical Graduates
								</p>
							</motion.div>
						</div>
					</motion.footer>
				</BrowserRouter>
			</TooltipProvider>
		</QueryClientProvider>
	);
};

export default App;
