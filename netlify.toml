[build]
  base = "/"
  command = "npm run build"
  publish = "client/dist"
  ignore = "git diff --quiet $CACHED_COMMIT_REF $COMMIT_REF ./"

[build.environment]
  NODE_VERSION = "18"
  NPM_FLAGS = "--no-audit --no-fund"

# Redirect API requests to the backend
[[redirects]]
  from = "/api/*"
  to = "/.netlify/functions/server/:splat"
  status = 200

# SPA fallback
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# Headers for security and caching
[[headers]]
  for = "/*"
  [headers.values]
    X-Frame-Options = "DENY"
    X-XSS-Protection = "1; mode=block"
    X-Content-Type-Options = "nosniff"
    Referrer-Policy = "strict-origin-when-cross-origin"
    Content-Security-Policy = "default-src 'self'; img-src 'self' data: https:; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline';"