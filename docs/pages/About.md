# About Page Documentation

## Component Architecture

```mermaid
graph TD
    subgraph AboutPage[About Page Container]
        style AP fill:#e63946
        style Hero fill:#457b9d
        style Mission fill:#2a9d8f
        style Team fill:#457b9d
        style Contact fill:#2a9d8f

        AP[About Page] --> Hero[Hero Section]
        AP --> Mission[Mission Section]
        AP --> Team[Team Section]
        AP --> Contact[Contact Section]

        subgraph HeroSection[Hero Section Components]
            Hero --> HeroTitle[Title]
            Hero --> HeroContent[Content]
            Hero --> HeroImage[Background Image]
        end

        subgraph MissionSection[Mission & Values]
            Mission --> MissionStatement[Mission Statement]
            Mission --> Values[Core Values]
            Values --> ValueCards[Value Cards]
            Mission --> Impact[Impact Stats]
        end

        subgraph TeamSection[Team Members]
            Team --> TeamGrid[Team Grid]
            TeamGrid --> TeamCard[Team Member Card]
            TeamCard --> MemberInfo[Member Info]
            TeamCard --> MemberBio[Biography]
            TeamCard --> Socials[Social Links]
        end

        subgraph ContactSection[Contact Information]
            Contact --> ContactForm[Contact Form]
            Contact --> LocationInfo[Location Info]
            Contact --> SocialMedia[Social Media]
            ContactForm --> FormValidation[Form Validation]
            ContactForm --> SubmitHandler[Submit Handler]
        end
    end
```

## State Management

```mermaid
stateDiagram-v2
    [*] --> LoadingState
    LoadingState --> ContentLoaded
    ContentLoaded --> DisplayingContent

    state ContactForm {
        [*] --> FormInitial
        FormInitial --> FormValidating: Submit
        FormValidating --> FormSuccess: Valid
        FormValidating --> FormError: Invalid
        FormSuccess --> FormInitial: Reset
        FormError --> FormInitial: Reset
    }

    state TeamSection {
        [*] --> LoadingTeam
        LoadingTeam --> TeamLoaded
        TeamLoaded --> DisplayingTeam
        DisplayingTeam --> ShowingMemberDetails: Click Member
        ShowingMemberDetails --> DisplayingTeam: Close Details
    }
```

## User Interaction Flow

```mermaid
sequenceDiagram
    participant User
    participant Page as About Page
    participant API as Backend API
    participant Form as Contact Form

    User->>Page: Visit About Page
    Page->>API: Request Page Data
    API-->>Page: Return Content
    Page->>User: Display Content

    User->>Page: Click Team Member
    Page->>User: Show Member Details

    User->>Form: Fill Contact Form
    Form->>Form: Validate Input
    Form->>API: Submit Form Data
    API-->>Form: Confirm Submission
    Form->>User: Show Success Message
```

## Component Relationships

```mermaid
graph LR
    subgraph StateManagement[State Management]
        style PageState fill:#e63946
        style TeamState fill:#457b9d
        style ContactState fill:#2a9d8f
        style LoadingState fill:#457b9d

        PageState[Page State] --> TeamState[Team State]
        PageState --> ContactState[Contact Form State]
        PageState --> LoadingState[Loading State]
    end

    subgraph Components[Component Tree]
        style AboutPage fill:#e63946
        style TeamSection fill:#457b9d
        style ContactSection fill:#2a9d8f

        AboutPage --> HeroSection
        AboutPage --> MissionSection
        AboutPage --> TeamSection
        AboutPage --> ContactSection

        TeamSection --> TeamGrid
        TeamGrid --> TeamCard

        ContactSection --> ContactForm
        ContactForm --> ValidationLogic
    end

    subgraph DataFlow[Data Flow]
        style API fill:#e63946
        style ContactForm fill:#2a9d8f

        API[API Service] --> PageState
        ContactForm --> API
    end
```
