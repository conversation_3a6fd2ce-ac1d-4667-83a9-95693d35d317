# MockExams Page Documentation

## Visual Layout

```mermaid
graph TB
    subgraph Layout["Page Layout Structure"]
        style Header fill:#e63946
        style Content fill:#457b9d
        style ExamArea fill:#2a9d8f

        Header["Header Section"] --> Title["Page Title"]
        Header --> Overview["Exam Overview"]

        Content["Main Content"] --> ExamList["Available Exams"]
        ExamList --> ExamCard["Exam Card"]

        ExamArea["Exam Area"] --> Questions["Question Display"]
        ExamArea --> Timer["Exam Timer"]
        ExamArea --> Progress["Progress Tracker"]
    end
```

## Component Relationships

```mermaid
graph LR
    subgraph Components["Component Hierarchy"]
        style ExamPage fill:#e63946
        style MainContent fill:#457b9d
        style ExamTools fill:#2a9d8f

        ExamPage["ExamPage"] --> Header["Header"]
        ExamPage --> MainContent["MainContent"]
        ExamPage --> ExamTools["ExamTools"]

        MainContent --> ExamList["ExamList"]
        ExamList --> ExamCard["ExamCard"]
        ExamCard --> ExamInfo["ExamInfo"]

        ExamTools --> QuestionDisplay["QuestionDisplay"]
        ExamTools --> Timer["Timer"]
        ExamTools --> ProgressTracker["ProgressTracker"]
    end
```

## User Interaction Flow

```mermaid
sequenceDiagram
    participant User
    participant Page as MockExams Page
    participant API as Backend API
    participant Timer as Timer System

    User->>Page: Access MockExams
    Page->>API: Fetch Exam List
    API-->>Page: Return Available Exams
    Page->>User: Display Exams

    User->>Page: Select Exam
    Page->>Timer: Start Exam Timer
    Timer->>User: Display Time Remaining

    User->>Page: Submit Answers
    Page->>API: Process Submission
    API-->>Page: Return Results
    Page->>User: Show Score & Analysis
```

## State Management

```mermaid
graph LR
    subgraph StateManagement["State Management"]
        style ExamState fill:#e63946
        style QuestionState fill:#457b9d
        style TimerState fill:#2a9d8f

        ExamState["Exam State"] --> CurrentExam["Current Exam"]
        ExamState --> QuestionState["Question State"]
        ExamState --> TimerState["Timer State"]

        QuestionState --> CurrentQuestion["Current Question"]
        QuestionState --> UserAnswers["User Answers"]

        TimerState --> TimeRemaining["Time Remaining"]
        TimerState --> ExamStatus["Exam Status"]
    end
```
