# CasperPrep Page Documentation

## Visual Layout

```mermaid
graph TB
    subgraph Layout["Page Layout Structure"]
        style Header fill:#e63946
        style Content fill:#457b9d
        style Practice fill:#2a9d8f

        Header["Header Section"] --> Title["Page Title"]
        Header --> Overview["CASPer Overview"]

        Content["Main Content"] --> Modules["Learning Modules"]
        Modules --> ModuleCard["Module Card"]

        Practice["Practice Area"] --> Scenarios["Practice Scenarios"]
        Practice --> Timer["Scenario Timer"]
        Practice --> Response["Response Editor"]
    end
```

## Component Relationships

```mermaid
graph LR
    subgraph Components["Component Hierarchy"]
        style CasperPage fill:#e63946
        style MainContent fill:#457b9d
        style PracticeTools fill:#2a9d8f

        CasperPage["CasperPage"] --> Header["Header"]
        CasperPage --> MainContent["MainContent"]
        CasperPage --> PracticeTools["PracticeTools"]

        MainContent --> ModuleList["ModuleList"]
        ModuleList --> ModuleCard["ModuleCard"]
        ModuleCard --> Progress["ProgressTracker"]

        PracticeTools --> ScenarioPlayer["ScenarioPlayer"]
        PracticeTools --> Timer["Timer"]
        PracticeTools --> ResponseEditor["ResponseEditor"]
    end
```

## User Interaction Flow

```mermaid
sequenceDiagram
    participant User
    participant Page as CasperPrep Page
    participant API as Backend API
    participant Timer as Timer System

    User->>Page: Access CasperPrep
    Page->>API: Fetch Modules
    API-->>Page: Return Module List
    Page->>User: Display Modules

    User->>Page: Select Scenario
    Page->>Timer: Start Timer
    Timer->>User: Display Countdown

    User->>Page: Submit Response
    Page->>API: Save Response
    API-->>Page: Feedback
    Page->>User: Show Results
```

## State Management

```mermaid
graph LR
    subgraph StateManagement["State Management"]
        style CasperState fill:#e63946
        style ScenarioState fill:#457b9d
        style TimerState fill:#2a9d8f

        CasperState["Casper State"] --> ModuleProgress["Module Progress"]
        CasperState --> ScenarioState["Scenario State"]
        CasperState --> TimerState["Timer State"]

        ScenarioState --> CurrentScenario["Current Scenario"]
        ScenarioState --> UserResponse["User Response"]

        TimerState --> TimeRemaining["Time Remaining"]
        TimerState --> TimerStatus["Timer Status"]
    end
```
