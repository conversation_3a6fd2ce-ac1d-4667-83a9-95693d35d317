# IMG Dream Support - Platform Architecture

## System Context (C4 Model)

```mermaid
graph TB
    subgraph Context[System Context]
        IDS[IMG Dream Support System] --> IMG[International Medical Graduates]
        IDS --> Mentors[Medical Mentors]
        IDS --> Admin[Administrative Staff]

        subgraph ExternalSystems[External Systems]
            IDS --> Payment[Payment Gateway]
            IDS --> Email[Email Service]
            IDS --> Calendar[Calendar System]
        end
    end
```

## Container Architecture

```mermaid
graph TB
    subgraph Containers[System Containers]
        Web[Web Application] --> API[API Application]
        API --> DB[(Database)]
        API --> Cache[(Cache)]

        subgraph ExternalServices[External Services]
            API --> EmailService[Email Service]
            API --> PaymentGateway[Payment Gateway]
            API --> StorageService[File Storage]
        end

        subgraph WebComponents[Web Application]
            Web --> Auth[Authentication]
            Web --> Booking[Booking System]
            Web --> Resources[Resource Management]
            Web --> Community[Community Platform]
        end
    end
```

## Component Architecture

```mermaid
graph TD
    subgraph Components[Core Components]
        Auth[Authentication Service] --> UserMgmt[User Management]
        Auth --> SessionMgmt[Session Management]

        Booking[Booking System] --> Calendar[Calendar Management]
        Booking --> Payment[Payment Processing]
        Booking --> Notification[Notification Service]

        Resources[Resource System] --> ContentMgmt[Content Management]
        Resources --> FileStorage[File Storage]
        Resources --> Search[Search Engine]

        Community[Community Platform] --> Forum[Forum System]
        Community --> Matching[Study Partner Matching]
        Community --> Messaging[Messaging System]
    end
```

## Database Schema

```mermaid
erDiagram
    USERS ||--o{ BOOKINGS : makes
    USERS ||--o{ RESOURCES : accesses
    USERS ||--o{ FORUM_POSTS : creates
    MENTORS ||--o{ BOOKINGS : handles
    RESOURCES ||--o{ CATEGORIES : belongs_to
    BOOKINGS ||--|| PAYMENTS : has
```

## Page Architecture

### Home Page

```mermaid
graph TD
    subgraph HomePage[Home Page Layout]
        Hero[Hero Section] --> HeroContent["Content (Text + CTA)"]
        Hero --> HeroImage["Background Image"]

        subgraph MainSections[Main Page Sections]
            Approach[Our Approach] --> ApproachSteps["Step-by-Step Guide"]
            Resources[Resources] --> ResourceTypes["Guides, Videos, Articles"]
            Coaching[Coaching & Mentorship] --> CoachingTypes["1-on-1, Group, Workshops"]
            Exam[Exam Preparation] --> ExamTypes["MCCQE, NAC OSCE, USMLE"]
        end

        subgraph SuccessSection[Success Stories]
            Stories[Success Stories] --> Testimonials[Testimonials]
            Stories --> Cases[Case Studies]
            Stories --> Metrics[Success Metrics]
            Metrics --> PassRate["Exam Pass Rate"]
            Metrics --> MatchRate["Residency Match Rate"]
        end

        subgraph CTASection[Call to Action]
            CTA[Call to Action] --> Newsletter[Newsletter Sign-up]
            CTA --> Consultation[Book Consultation]
            Newsletter --> Form["Email Form"]
            Consultation --> Calendar["Booking Calendar"]
        end
    end
```

### Services Page

```mermaid
graph TD
    subgraph ServicesPage[Services Page Layout]
        Header[Header Section] --> Title["Services Title"]
        Header --> Description["Services Overview"]

        subgraph MainServices[Core Services]
            QB[Question Banks] --> QBFeatures["Practice Tests, Analytics"]
            ME[Mock Exams] --> MEFeatures["Timed Tests, Feedback"]

            subgraph ResidencyApp[Residency Application]
                RA[Residency Application] --> CaRMS[CaRMS Support]
                RA --> ERAS[ERAS Support]
                RA --> PS[Personal Statement]
                RA --> CV[CV Optimization]
                PS --> PSReview["Review & Feedback"]
                CV --> CVTemplate["Templates & Tips"]
            end

            subgraph InterviewPrep[Interview Preparation]
                IP[Interview Preparation] --> MMI[MMI Training]
                IP --> RI[Residency Interview]
                IP --> BQ[Behavioral Questions]
                MMI --> Scenarios["Practice Scenarios"]
                RI --> MockInterviews["Mock Interviews"]
            end
        end

        subgraph Resources[Resources Section]
            R[Resources] --> Blog[Blog]
            R --> Webinars[Webinars]
            R --> Guides[Guides]
            R --> Checklists[Checklists]
            Blog --> Articles["Latest Articles"]
            Webinars --> Schedule["Upcoming Sessions"]
        end

        subgraph StudyPartner[Study Partner Feature]
            SPM[Study Partner Matching] --> Algo["Matching Algorithm"]
            SPM --> Groups["Study Groups"]
            SPM --> Community["Community Forum"]
        end
    end
```

## Deployment Architecture

```mermaid
graph TB
    subgraph Cloud[Cloud Infrastructure]
        LB[Load Balancer] --> WebApp1[Web App Instance 1]
        LB --> WebApp2[Web App Instance 2]
        WebApp1 --> API1[API Instance 1]
        WebApp2 --> API2[API Instance 2]
        API1 --> DB[(Primary Database)]
        API2 --> DB
        API1 --> Cache[(Redis Cache)]
        API2 --> Cache
    end
```

## Security Architecture

```mermaid
graph TD
    subgraph Security[Security Layers]
        WAF[Web Application Firewall] --> SSL[SSL/TLS Encryption]
        SSL --> Auth[Authentication]
        Auth --> RBAC[Role-Based Access Control]
        RBAC --> DataEnc[Data Encryption]
    end
```

## Technical Stack

- **Frontend**: React with TypeScript
- **Build Tool**: Vite
- **Styling**: Tailwind CSS
- **Backend**: Express.js
- **Database**: PostgreSQL
- **Caching**: Redis
- **Search**: Elasticsearch
- **File Storage**: AWS S3
- **CDN**: Cloudflare
- **Monitoring**: DataDog

## Key Features

### Authentication System

- JWT-based authentication
- Role-based access control
- OAuth2 integration
- Password recovery workflow

### Booking System

- Integrated calendar system
- Payment gateway integration
- Automated confirmation emails
- Scheduling conflict resolution

### Resource Management

- Downloadable guides and checklists
- Blog content management
- Webinar scheduling and recordings
- Search functionality

### Study Partner Matching

- Algorithm-based matching system
- Community building features
- Study group formation
- Real-time messaging

### Newsletter System

- Automated email campaigns
- Subscription management
- Analytics tracking
- Content personalization
