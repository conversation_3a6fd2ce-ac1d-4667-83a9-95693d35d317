# IMG Dream Support

This project is a web application for IMG Dream Support, providing services and resources for International Medical Graduates.

## Project Structure

The project is organized into three main parts:

- `client`: Contains the frontend React application
- `server`: Contains the backend Express server
- `platform`: Contains shared types and utilities used by both client and server

## Prerequisites

- Node.js (v14 or later)
- npm (v6 or later)

## Getting Started

1. Clone the repository:
   ```
   git clone https://github.com/your-username/imgdreamsupport.git
   cd imgdreamsupport
   ```

2. Install dependencies for all parts of the project:
   ```
   npm run install:all
   ```

3. Build the shared platform package:
   ```
   cd platform && npm run build && cd ..
   ```

4. Start the development server (both frontend and backend):
   ```
   npm run dev
   ```

   This will start the backend server on http://localhost:5000 and the frontend development server on http://localhost:3000.

## Building for Production

To build all packages for production:

```
npm run build
```

## Running in Production

To start the production server:

```
npm start
```

This will serve the built frontend from the backend server.

## Project Structure

- `client/`: Frontend React application (TypeScript)
  - `src/`: Source files for the React app
    - `components/`: React components
    - `pages/`: Page components
    - `App.tsx`: Main App component
- `server/`: Backend Express server (TypeScript)
  - `src/`: Source files for the server
    - `index.ts`: Main server file
- `platform/`: Shared code
  - `types/`: TypeScript type definitions

## Scripts

- `npm run dev`: Start both client and server in development mode
- `npm run build`: Build all packages for production
- `npm start`: Start the production server
- `npm run install:all`: Install dependencies for all packages

## Contributing

Please read CONTRIBUTING.md for details on our code of conduct, and the process for submitting pull requests.

## License

This project is licensed under the MIT License - see the LICENSE.md file for details.


