export interface BlogPost {
    id: string;
    title: string;
    content: string;
    author: string;
    date: string;
    category: string;
}
export interface Service {
    id: string;
    name: string;
    description: string;
    price: number;
}
export interface StudyPartner {
    id: string;
    name: string;
    email: string;
    exam: string;
    studyHours: number;
}
export interface StudyPartnerProfile {
    id: string;
    name: string;
    email: string;
    studyTimes: string[];
    topicsOfInterest: string[];
    examDate: string;
    additionalInfo?: string;
}
export type Category = 'USMLE' | 'IMG' | 'Residency' | 'General';
export interface SubscribeFormData {
    email: string;
    name: string;
}
